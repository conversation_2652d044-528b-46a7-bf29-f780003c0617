<?php

namespace Tests\Feature;

use App\Models\Refs;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class ReferralSystemTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function user_can_generate_referral_code()
    {
        $user = User::factory()->create();

        $referralCode = $user->generateReferralCode();

        $this->assertNotEmpty($referralCode);
        $this->assertEquals(6, strlen($referralCode));
        $this->assertEquals($referralCode, $user->fresh()->referral_code);
    }

    #[Test]
    public function referral_codes_are_unique()
    {
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();

        $code1 = $user1->generateReferralCode();
        $code2 = $user2->generateReferralCode();

        $this->assertNotEquals($code1, $code2);
    }

    #[Test]
    public function user_can_get_referral_link()
    {
        $user = User::factory()->create();
        $user->generateReferralCode();

        $link = $user->getReferralLink();

        $this->assertStringContainsString($user->referral_code, $link);
        $this->assertStringContainsString('/ref/', $link);
    }

    #[Test]
    public function can_validate_referral_code()
    {
        $referrer = User::factory()->create();
        $referrer->generateReferralCode();

        $validatedUser = User::validateReferralCode($referrer->referral_code);

        $this->assertNotNull($validatedUser);
        $this->assertEquals($referrer->id, $validatedUser->id);
    }

    #[Test]
    public function invalid_referral_code_returns_null()
    {
        $validatedUser = User::validateReferralCode('INVALID');

        $this->assertNull($validatedUser);
    }

    #[Test]
    public function can_create_referral_relationship()
    {
        $referrer = User::factory()->create();
        $referred = User::factory()->create();

        $success = $referred->createReferralRelationship($referrer);

        $this->assertTrue($success);
        $this->assertDatabaseHas('refs', [
            'user_id' => $referrer->id,
            'ref_id' => $referred->id,
        ]);
    }

    #[Test]
    public function cannot_refer_yourself()
    {
        $user = User::factory()->create();

        $canRefer = $user->canBeReferredBy($user);
        $success = $user->createReferralRelationship($user);

        $this->assertFalse($canRefer);
        $this->assertFalse($success);
    }

    #[Test]
    public function cannot_be_referred_twice()
    {
        $referrer1 = User::factory()->create();
        $referrer2 = User::factory()->create();
        $referred = User::factory()->create();

        // First referral should succeed
        $success1 = $referred->createReferralRelationship($referrer1);
        $this->assertTrue($success1);

        // Second referral should fail
        $canRefer = $referred->canBeReferredBy($referrer2);
        $success2 = $referred->createReferralRelationship($referrer2);

        $this->assertFalse($canRefer);
        $this->assertFalse($success2);
    }

    #[Test]
    public function new_user_registration_with_referral_code()
    {
        $referrer = User::factory()->create();
        $referrer->generateReferralCode();

        $payload = [
            'telegram_id' => '123456789',
            'name' => 'John Doe',
            'country' => 'US',
            'language_code' => 'en',
            'referral_code' => $referrer->referral_code,
        ];

        $response = $this->postJson('/api/users/sync', $payload);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'user' => [
                        'id',
                        'referral_code',
                    ],
                    'referral' => [
                        'referred_by',
                        'referral_created',
                    ],
                ],
            ]);

        // Verify referral relationship was created
        $newUser = User::where('tele_id', '123456789')->first();
        $this->assertDatabaseHas('refs', [
            'user_id' => $referrer->id,
            'ref_id' => $newUser->id,
        ]);
    }

    #[Test]
    public function new_user_registration_with_invalid_referral_code()
    {
        $payload = [
            'telegram_id' => '123456789',
            'name' => 'John Doe',
            'country' => 'US',
            'language_code' => 'en',
            'referral_code' => 'INVALID',
        ];

        $response = $this->postJson('/api/users/sync', $payload);

        $response->assertStatus(201)
            ->assertJsonMissing(['referral']);

        // Verify no referral relationship was created
        $newUser = User::where('tele_id', '123456789')->first();
        $this->assertDatabaseMissing('refs', [
            'ref_id' => $newUser->id,
        ]);
    }

    #[Test]
    public function can_validate_referral_code_via_api()
    {
        $referrer = User::factory()->create();
        $referrer->generateReferralCode();

        $response = $this->postJson('/api/public/referral/validate', [
            'referral_code' => $referrer->referral_code,
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'valid' => true,
                    'referrer' => [
                        'id' => $referrer->id,
                        'name' => $referrer->name,
                    ],
                ],
            ]);
    }

    #[Test]
    public function api_returns_error_for_invalid_referral_code()
    {
        $response = $this->postJson('/api/public/referral/validate', [
            'referral_code' => 'INVALID',
        ]);

        $response->assertStatus(404)
            ->assertJson([
                'success' => false,
                'data' => [
                    'valid' => false,
                    'referrer' => null,
                ],
            ]);
    }

    #[Test]
    public function authenticated_user_can_get_referral_code()
    {
        $user = User::factory()->create();
        $token = $user->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer '.$token,
        ])->getJson('/api/user/referral/code');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'referral_code',
                ],
            ]);
    }

    #[Test]
    public function authenticated_user_can_get_referral_link()
    {
        $user = User::factory()->create();
        $token = $user->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer '.$token,
        ])->getJson('/api/user/referral/link');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'referral_link',
                    'referral_code',
                ],
            ]);
    }

    #[Test]
    public function authenticated_user_can_get_referral_stats()
    {
        $referrer = User::factory()->create();
        $referred1 = User::factory()->create();
        $referred2 = User::factory()->create();

        // Create referral relationships
        $referred1->createReferralRelationship($referrer);
        $referred2->createReferralRelationship($referrer);

        $token = $referrer->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer '.$token,
        ])->getJson('/api/user/referral/stats');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'stats' => [
                        'total_referrals',
                        'total_commission',
                        'referral_code',
                        'referral_link',
                    ],
                    'recent_referrals',
                ],
            ])
            ->assertJson([
                'data' => [
                    'stats' => [
                        'total_referrals' => 2,
                    ],
                ],
            ]);
    }

    #[Test]
    public function authenticated_user_can_regenerate_referral_code()
    {
        $user = User::factory()->create();
        $originalCode = $user->generateReferralCode();
        $token = $user->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer '.$token,
        ])->postJson('/api/user/referral/regenerate');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'referral_code',
                    'referral_link',
                ],
            ]);

        $newCode = $response->json('data.referral_code');
        $this->assertNotEquals($originalCode, $newCode);
    }

    #[Test]
    public function refs_model_validation_methods_work()
    {
        $referrer = User::factory()->create();
        $referred = User::factory()->create();

        // Test relationship doesn't exist initially
        $this->assertFalse(Refs::relationshipExists($referrer->id, $referred->id));

        // Test validation passes
        $errors = Refs::validateReferralRelationship($referrer->id, $referred->id);
        $this->assertEmpty($errors);

        // Create referral
        $result = Refs::createReferral($referrer->id, $referred->id);
        $this->assertTrue($result['success']);
        $this->assertEmpty($result['errors']);

        // Test relationship now exists
        $this->assertTrue(Refs::relationshipExists($referrer->id, $referred->id));

        // Test validation fails for duplicate
        $errors = Refs::validateReferralRelationship($referrer->id, $referred->id);
        $this->assertNotEmpty($errors);
    }

    #[Test]
    public function refs_model_helper_methods_work()
    {
        $referrer = User::factory()->create();
        $referred1 = User::factory()->create();
        $referred2 = User::factory()->create();

        // Create referrals with commission
        Refs::createReferral($referrer->id, $referred1->id, 10.5);
        Refs::createReferral($referrer->id, $referred2->id, 5.25);

        // Test helper methods
        $totalCommission = Refs::getTotalCommissionForReferrer($referrer->id);
        $referralCount = Refs::getReferralCountForReferrer($referrer->id);
        $recentReferrals = Refs::getRecentReferralsForReferrer($referrer->id, 5);

        $this->assertEquals(15.75, $totalCommission);
        $this->assertEquals(2, $referralCount);
        $this->assertCount(2, $recentReferrals);
    }
}
