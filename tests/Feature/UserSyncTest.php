<?php

namespace Tests\Feature;

use App\Models\User;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class UserSyncTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function can_initialize_new_user()
    {
        $payload = [
            'telegram_id' => '123456789',
            'name' => 'John Do<PERSON>',
            'country' => 'US',
            'language_code' => 'en',
        ];

        $response = $this->postJson('/api/users/sync', $payload);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'user' => [
                        'id',
                        'tele_id',
                        'name',
                        'country',
                        'language_code',
                        'avatar_url',
                        'last_active',
                        'balance_ton',
                        'balance_token',
                        'created_at',
                        'updated_at',
                    ],
                    'action',
                ],
            ])
            ->assertJson([
                'success' => true,
                'message' => 'User initialized successfully',
                'data' => [
                    'user' => [
                        'tele_id' => '123456789',
                        'name' => '<PERSON>',
                        'country' => 'US',
                        'language_code' => 'en',
                        'balance_ton' => '0.00000000',
                        'balance_token' => '0.00000000',
                    ],
                    'action' => 'initialized',
                ],
            ]);

        // Verify user was created in database
        $this->assertDatabaseHas('users', [
            'tele_id' => '123456789',
            'name' => 'John Doe',
            'country' => 'US',
            'language_code' => 'en',
        ]);
    }

    #[Test]
    public function can_restore_existing_user()
    {
        // Create an existing user
        $existingUser = User::factory()->create([
            'tele_id' => '987654321',
            'name' => 'Jane Smith',
            'country' => 'CA',
            'language_code' => 'fr',
            'last_active' => Carbon::now()->subHours(24), // 24 hours ago
            'balance_ton' => 10.50000000,
            'balance_token' => 5.25000000,
        ]);

        $payload = [
            'telegram_id' => '987654321',
            'name' => 'Jane Updated', // Different name in payload
            'country' => 'UK', // Different country in payload
            'language_code' => 'en', // Different language in payload
        ];

        $response = $this->postJson('/api/users/sync', $payload);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'User restored successfully',
                'data' => [
                    'user' => [
                        'id' => $existingUser->id,
                        'tele_id' => '987654321',
                        'name' => 'Jane Smith', // Should keep original name
                        'country' => 'CA', // Should keep original country
                        'language_code' => 'fr', // Should keep original language
                        'balance_ton' => '10.50000000', // Should keep original balance
                        'balance_token' => '5.25000000', // Should keep original balance
                    ],
                    'action' => 'restored',
                ],
            ]);

        // Verify only last_active was updated
        $existingUser->refresh();
        $this->assertEquals('Jane Smith', $existingUser->name); // Name unchanged
        $this->assertEquals('CA', $existingUser->country); // Country unchanged
        $this->assertEquals('fr', $existingUser->language_code); // Language unchanged
        $this->assertTrue($existingUser->last_active->isToday()); // last_active updated
    }

    #[Test]
    public function sync_requires_telegram_id()
    {
        $payload = [
            'name' => 'John Doe',
            'country' => 'US',
            'language_code' => 'en',
        ];

        $response = $this->postJson('/api/users/sync', $payload);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => 'Validation failed',
            ])
            ->assertJsonValidationErrors(['telegram_id']);
    }

    #[Test]
    public function sync_requires_name()
    {
        $payload = [
            'telegram_id' => '123456789',
            'country' => 'US',
            'language_code' => 'en',
        ];

        $response = $this->postJson('/api/users/sync', $payload);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => 'Validation failed',
            ])
            ->assertJsonValidationErrors(['name']);
    }

    #[Test]
    public function sync_allows_nullable_country_and_language()
    {
        $payload = [
            'telegram_id' => '123456789',
            'name' => 'John Doe',
            // country and language_code are optional
        ];

        $response = $this->postJson('/api/users/sync', $payload);

        $response->assertStatus(201)
            ->assertJson([
                'success' => true,
                'message' => 'User initialized successfully',
                'data' => [
                    'user' => [
                        'tele_id' => '123456789',
                        'name' => 'John Doe',
                        'country' => null,
                        'language_code' => null,
                    ],
                    'action' => 'initialized',
                ],
            ]);
    }

    #[Test]
    public function sync_validates_name_length()
    {
        $payload = [
            'telegram_id' => '123456789',
            'name' => str_repeat('a', 256), // Too long
            'country' => 'US',
            'language_code' => 'en',
        ];

        $response = $this->postJson('/api/users/sync', $payload);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['name']);
    }

    #[Test]
    public function sync_validates_country_length()
    {
        $payload = [
            'telegram_id' => '123456789',
            'name' => 'John Doe',
            'country' => str_repeat('a', 256), // Too long
            'language_code' => 'en',
        ];

        $response = $this->postJson('/api/users/sync', $payload);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['country']);
    }

    #[Test]
    public function sync_validates_language_code_length()
    {
        $payload = [
            'telegram_id' => '123456789',
            'name' => 'John Doe',
            'country' => 'US',
            'language_code' => 'this_is_too_long', // Too long
        ];

        $response = $this->postJson('/api/users/sync', $payload);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['language_code']);
    }

    #[Test]
    public function multiple_syncs_of_same_user_updates_last_active()
    {
        // First sync - create user
        $payload = [
            'telegram_id' => '123456789',
            'name' => 'John Doe',
            'country' => 'US',
            'language_code' => 'en',
        ];

        $firstResponse = $this->postJson('/api/users/sync', $payload);
        $firstResponse->assertStatus(201);

        // Wait a moment and sync again
        sleep(1);

        $secondResponse = $this->postJson('/api/users/sync', $payload);
        $secondResponse->assertStatus(200)
            ->assertJson([
                'data' => ['action' => 'restored'],
            ]);

        // Verify only one user exists
        $this->assertEquals(1, User::where('tele_id', '123456789')->count());

        // Verify last_active was updated
        $user = User::where('tele_id', '123456789')->first();
        $this->assertTrue($user->last_active->isToday());
    }
}
