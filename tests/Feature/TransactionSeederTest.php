<?php

namespace Tests\Feature;

use App\Enums\TransactionStatus;
use App\Enums\TransactionType;
use App\Models\Transaction;
use App\Models\User;
use Database\Seeders\TransactionSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TransactionSeederTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test that the TransactionSeeder creates a johndoe user and transactions
     */
    public function test_transaction_seeder_creates_johndoe_user_and_transactions(): void
    {
        // Ensure no johndoe user exists initially
        $this->assertDatabaseMissing('users', ['username' => 'johndoe']);

        // Run the seeder
        $this->seed(TransactionSeeder::class);

        // Assert johndoe user was created
        $this->assertDatabaseHas('users', [
            'username' => 'johndoe',
            'name' => 'John Doe',
            'country' => 'US',
            'language_code' => 'en',
        ]);

        // Get the created user
        $user = User::where('username', 'johndoe')->first();
        $this->assertNotNull($user);

        // Assert transactions were created for johndoe
        $transactions = Transaction::where('user_id', $user->id)->get();
        $this->assertGreaterThan(0, $transactions->count());

        // Assert we have different types of transactions
        $transactionTypes = $transactions->pluck('type')->map(function($type) {
            return $type instanceof TransactionType ? $type->value : $type;
        })->unique();
        $this->assertContains(TransactionType::BOOST_PAYMENT, $transactionTypes);
        $this->assertContains(TransactionType::REFERRAL_BONUS, $transactionTypes);
        $this->assertContains(TransactionType::WITHDRAWAL, $transactionTypes);

        // Assert we have different statuses
        $transactionStatuses = $transactions->pluck('status')->map(function($status) {
            return $status instanceof TransactionStatus ? $status->value : $status;
        })->unique();
        $this->assertContains(TransactionStatus::COMPLETED, $transactionStatuses);
        $this->assertContains(TransactionStatus::PENDING, $transactionStatuses);

        // Assert all transactions have required fields
        foreach ($transactions as $transaction) {
            $this->assertNotNull($transaction->hash);
            $this->assertNotNull($transaction->description);
            $this->assertGreaterThan(0, $transaction->value);
            $this->assertEquals($user->id, $transaction->user_id);
        }
    }

    /**
     * Test that running the seeder multiple times doesn't create duplicate transactions
     */
    public function test_transaction_seeder_handles_existing_user(): void
    {
        // Create johndoe user manually first
        $existingUser = User::create([
            'name' => 'John Doe',
            'username' => 'johndoe',
            'tele_id' => '888777666',
            'country' => 'CA',
            'language_code' => 'fr',
            'balance_ton' => 10.00,
            'balance_token' => 1000.00,
            'speed' => 2.00,
            'pending_token' => 50.00,
        ]);

        // Run the seeder
        $this->seed(TransactionSeeder::class);

        // Assert only one johndoe user exists
        $users = User::where('username', 'johndoe')->get();
        $this->assertEquals(1, $users->count());

        // Assert the existing user was used (not recreated)
        $user = $users->first();
        $this->assertEquals($existingUser->id, $user->id);

        // Assert transactions were still created
        $transactions = Transaction::where('user_id', $user->id)->get();
        $this->assertGreaterThan(0, $transactions->count());
    }

    /**
     * Test that transaction hashes are unique
     */
    public function test_transaction_hashes_are_unique(): void
    {
        // Run the seeder
        $this->seed(TransactionSeeder::class);

        // Get all transactions for johndoe
        $user = User::where('username', 'johndoe')->first();
        $transactions = Transaction::where('user_id', $user->id)->get();

        // Assert all hashes are unique
        $hashes = $transactions->pluck('hash');
        $uniqueHashes = $hashes->unique();
        $this->assertEquals($hashes->count(), $uniqueHashes->count());

        // Assert all hashes follow the expected format
        foreach ($hashes as $hash) {
            $this->assertStringStartsWith('tx_', $hash);
            $this->assertEquals(35, strlen($hash)); // 'tx_' + 32 random characters
        }
    }
}
