<?php

namespace Modules\TelegramStarPayment\Providers;

use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Modules\TelegramBot\Events\TelegramPreCheckoutReceived;
use Modules\TelegramBot\Events\TelegramPaymentSuccessful;
use Modules\TelegramStarPayment\Listeners\PreCheckoutListener;
use Modules\TelegramStarPayment\Listeners\PaymentSuccessfulListener;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event handler mappings for the application.
     *
     * @var array<string, array<int, string>>
     */
    protected $listen = [
        TelegramPreCheckoutReceived::class => [
            PreCheckoutListener::class,
        ],
        TelegramPaymentSuccessful::class => [
            PaymentSuccessfulListener::class,
        ],
    ];

    /**
     * Indicates if events should be discovered.
     *
     * @var bool
     */
    protected static $shouldDiscoverEvents = true;

    /**
     * Configure the proper event listeners for email verification.
     */
    protected function configureEmailVerification(): void {}
}
