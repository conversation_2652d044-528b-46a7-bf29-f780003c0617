<?php

namespace Modules\TelegramStarPayment\Listeners;

use Mo<PERSON>les\TelegramBot\Events\TelegramPreCheckoutReceived;
use Illuminate\Support\Facades\Log;
use Modules\TelegramClient\Services\TelegramBotClient;

class PreCheckoutListener
{
    /**
     * Create the event listener.
     */
    public function __construct() {}

    /**
     * Handle the event.
     */
    public function handle(TelegramPreCheckoutReceived $event): void
    {
        try {
            $query = $event->webhookData['pre_checkout_query'];

            $this->answerPreCheckoutQuery($query['id'], true);
        } catch (\Exception $e) {
            Log::error('Error processing pre-checkout query', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'webhook_data' => $event->webhookData,
            ]);

            if (isset($query['id'])) {
                $this->answerPreCheckoutQuery($query['id'], false, 'Internal error occurred');
            }

            throw $e;
        }
    }

    /**
     * Answer the pre-checkout query
     */
    private function answerPreCheckoutQuery(string $queryId, bool $ok, ?string $errorMessage = null): void
    {
        try {
            $client = app(TelegramBotClient::class);
            $client->answerPreCheckoutQuery($queryId, $ok, $errorMessage);
        } catch (\Exception $e) {
            Log::error('Error answering pre-checkout query', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'query_id' => $queryId,
            ]);
        }
    }
}
