<?php

namespace Modules\TelegramStarPayment\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Modules\TelegramStarPayment\Http\Requests\CreateInvoiceRequest;
use Modules\TelegramClient\Services\TelegramBotClient;
use App\Helpers\TonHelper;
use Illuminate\Http\Request;
use Modules\TelegramStarPayment\Models\TelegramTransaction;
use Illuminate\Support\Str;

class TelegramStarPaymentController extends Controller
{
    private TelegramBotClient $telegramBotClient;

    public function __construct(TelegramBotClient $telegramBotClient)
    {
        $this->telegramBotClient = $telegramBotClient;
    }

    /**
     * Create a Telegram Stars invoice
     */
    public function createInvoice(CreateInvoiceRequest $request): JsonResponse
    {
        $user = User::find($request->user()->id);
        $amount = $request->input('amount');
        $transactionHash = Str::random(32);

        $payload = json_encode([
            'type' => 'star_to_ton',
            'amount' => $amount,
            'user_id' => $user->id,
            'ts' => time(),
            'transaction_hash' => $transactionHash
        ]);

        $totalNanoTon = app(TonHelper::class)->convertStarToNanoTon($amount);

        $invoiceData = [
            'amount' => $amount,
            'title' => number_format($totalNanoTon / 1000_000_000, 6, '.', '') . ' TON',
            'description' => 'Convert ' . $amount . ' Telegram Stars to TON',
            'payload' => $payload,
            'prices' => [
                [
                    'label' => 'Telegram Stars',
                    'amount' => $amount,
                ],
            ],
        ];

        $result = $this->telegramBotClient->createInvoiceLink($invoiceData);

        if ($result['success']) {
            return response()->json([
                'success' => true,
                'message' => 'Invoice created successfully',
                'data' => [
                    'invoice_link' => $result['invoice_link'],
                    'transaction_hash' => $transactionHash
                ]
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Failed to create invoice',
            'error' => $result['error']
        ], 500);
    }

    public function verifyInvoice(Request $request): JsonResponse
    {
        $transactionHash = $request->input('transaction_hash');
        $userId = $request->user()->id;

        if (!$transactionHash) {
            return response()->json([
                'success' => false,
                'message' => 'Transaction hash is required',
            ], 400);
        }

        $transaction = TelegramTransaction::where('transaction_hash', $transactionHash)
            ->where('user_id', $userId)
            ->first();

        if (!$transaction) {
            return response()->json([
                'success' => false,
                'message' => 'Invoice not found or user mismatch',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'message' => 'Invoice verified successfully',
        ]);
    }
}
