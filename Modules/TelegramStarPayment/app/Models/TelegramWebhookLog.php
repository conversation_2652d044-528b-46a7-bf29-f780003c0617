<?php

namespace Modules\TelegramStarPayment\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class TelegramWebhookLog extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['payload', 'status', 'error_message'];
    protected $table = 'telegram_payment_service_telegram_web_hook_log';

    // protected static function newFactory(): TelegramWebhookModelFactory
    // {
    //     // return TelegramWebhookModelFactory::new();
    // }
}
