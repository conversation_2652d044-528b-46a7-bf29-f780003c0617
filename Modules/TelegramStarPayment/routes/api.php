<?php

use Illuminate\Support\Facades\Route;
use Modules\TelegramStarPayment\Http\Controllers\TelegramStarPaymentController;

/*
|--------------------------------------------------------------------------
| Telegram Star Payment API Routes
|--------------------------------------------------------------------------
*/

Route::middleware(['user.auth'])->prefix('star-payment')->group(function () {
    Route::post('/create-invoice', [TelegramStarPaymentController::class, 'createInvoice']);

    Route::post('/verify-invoice', [TelegramStarPaymentController::class, 'verifyInvoice']);
});
