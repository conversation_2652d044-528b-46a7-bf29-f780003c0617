<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('telegram_webhook_logs', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('update_id')->nullable()->index();
            $table->string('update_type', 50)->nullable()->index();
            $table->json('payload');
            $table->enum('status', ['pending', 'processed', 'failed'])->default('pending')->index();
            $table->text('error_message')->nullable();
            $table->timestamp('processed_at')->nullable();
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->timestamps();

            // Indexes for better performance
            $table->index(['status', 'created_at']);
            $table->index(['update_type', 'status']);
            $table->index(['processed_at', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('telegram_webhook_logs');
    }
};
