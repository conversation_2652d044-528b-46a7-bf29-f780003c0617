# TelegramBot Module

A comprehensive Telegram Bot module for <PERSON><PERSON> using the `irazasyed/telegram-bot-sdk` package. This module provides webhook-based bot functionality with proper error handling, logging, and user management.

## Features

- 🤖 **Webhook Mode**: Efficient webhook-based message processing (no polling)
- 🔐 **Security**: Webhook secret verification and IP filtering
- 📊 **User Management**: Track and manage Telegram users
- 💬 **Message Handling**: Real-time message processing with command support
- 📝 **Logging**: Detailed logging of webhooks and errors
- 🛡️ **Error Handling**: Graceful error handling to prevent webhook failures
- 📈 **Statistics**: Bot usage statistics and analytics
- 🔧 **Admin Interface**: API endpoints for bot management

## Installation & Setup

### 1. Environment Configuration

Add the following environment variables to your `.env` file:

```env
# Required
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_WEBHOOK_URL=https://yourdomain.com/api/telegram-bot/webhook
TELEGRAM_WEBHOOK_SECRET=your_webhook_secret_here

# Optional
TELEGRAM_BOT_USERNAME=your_bot_username
TELEGRAM_BOT_NAME="Your Bot Name"
TELEGRAM_WEBHOOK_MAX_CONNECTIONS=40
TELEGRAM_LOG_WEBHOOK_REQUESTS=true
TELEGRAM_LOG_OUTGOING_MESSAGES=true
TELEGRAM_LOG_ERRORS=true
TELEGRAM_RATE_LIMITING_ENABLED=true
TELEGRAM_MAX_REQUESTS_PER_MINUTE=30
TELEGRAM_VERIFY_WEBHOOK_SECRET=true
TELEGRAM_ALLOWED_IPS="*************/20,**********/22"
```

### 2. Database Migration

The module migrations are automatically run when you run:

```bash
php artisan migrate
```

### 3. Generate Webhook Secret

Generate a secure webhook secret using the Artisan command:

```bash
# Generate and display a secure webhook secret
php artisan telegram:generate-webhook-secret

# Generate with custom length (16-128 characters)
php artisan telegram:generate-webhook-secret --length=32

# Generate and automatically update .env file
php artisan telegram:generate-webhook-secret --update-env

# Force overwrite existing secret in .env
php artisan telegram:generate-webhook-secret --update-env --force
```

### 4. Set Webhook URL

Use the admin API to set your webhook URL:

```bash
curl -X POST "https://yourdomain.com/api/admin/telegram-bot/webhook/set" \
  -H "Authorization: Bearer your_admin_token" \
  -H "Content-Type: application/json" \
  -d '{"url": "https://yourdomain.com/api/telegram-bot/webhook"}'
```

## API Endpoints

### Public Endpoints

- `GET|POST /api/telegram-bot/webhook` - Telegram webhook endpoint
- `GET /api/telegram-bot/test` - Module test endpoint

### Admin Endpoints (requires admin authentication)

- `GET /api/admin/telegram-bot/info` - Get bot information
- `GET /api/admin/telegram-bot/statistics` - Get bot statistics
- `POST /api/admin/telegram-bot/send-test-message` - Send test message
- `GET /api/admin/telegram-bot/users` - List users
- `GET /api/admin/telegram-bot/users/details` - Get user details
- `GET /api/admin/telegram-bot/webhook/info` - Get webhook info
- `POST /api/admin/telegram-bot/webhook/set` - Set webhook URL
- `POST /api/admin/telegram-bot/webhook/remove` - Remove webhook
- `GET /api/admin/telegram-bot/webhook/logs` - Get webhook logs

### Staff Endpoints (requires staff authentication)

- `GET /api/staff/telegram-bot/info` - Get bot information
- `GET /api/staff/telegram-bot/statistics` - Get bot statistics
- `GET /api/staff/telegram-bot/users` - List users

## Database Tables

### users (existing table, extended for Telegram)
Uses the existing users table with Telegram-specific fields:
- `tele_id` - Telegram user ID (unique)
- `name` - User's display name
- `username` - Telegram username
- `language_code` - User's language
- `last_active` - Last activity timestamp
- Other existing user fields (balance, referrals, etc.)

### telegram_webhook_logs
Tracks webhook requests:
- `update_id` - Telegram update ID
- `update_type` - Type of update
- `payload` - Full webhook payload (JSON)
- `status` - Processing status (pending, processed, failed)
- `error_message` - Error if processing failed
- `ip_address` - Request IP address
- `user_agent` - Request user agent

## Bot Commands

The module includes basic command handling:

- `/start` - Welcome message
- `/help` - Show available commands
- `/status` - Show user status and bot statistics

## Services

### TelegramBotService
Main service for bot operations:
- `getBotInfo()` - Get bot information
- `sendMessage()` - Send text message
- `sendPhoto()` - Send photo
- `editMessage()` - Edit message
- `deleteMessage()` - Delete message
- `answerCallbackQuery()` - Answer callback query

### WebhookService
Manages webhook operations:
- `setWebhook()` - Set webhook URL
- `removeWebhook()` - Remove webhook
- `getWebhookInfo()` - Get webhook information
- `verifyWebhookRequest()` - Verify webhook authenticity
- `processWebhookUpdate()` - Process incoming updates

### MessageHandlerService
Handles incoming messages:
- `handleUpdate()` - Process webhook updates
- `handleMessage()` - Process regular messages
- `handleCommand()` - Process bot commands
- `handleCallbackQuery()` - Process callback queries

## Configuration

The module configuration is located in `Modules/TelegramBot/config/config.php`. Key settings include:

- Bot credentials and settings
- Webhook configuration
- Message templates
- Command configuration
- Logging preferences
- Rate limiting settings
- Security options

## Security Features

- **Webhook Secret Verification**: Validates incoming webhooks using secret token
- **IP Filtering**: Restricts webhooks to Telegram's IP ranges
- **Rate Limiting**: Prevents abuse with configurable rate limits
- **Input Validation**: Validates all incoming data
- **Error Handling**: Graceful error handling to prevent webhook failures

## Extending the Module

### Adding New Commands

1. Add command configuration to `config/config.php`
2. Implement command handler in `MessageHandlerService`
3. Update help message

### Adding New Message Types

1. Extend `MessageHandlerService::handleUpdate()`
2. Add specific handlers for new message types
3. Update logging and statistics

### Custom Middleware

Add custom middleware to routes in `routes/api.php` for additional security or functionality.

## Artisan Commands

The module provides the following Artisan commands:

### Generate Webhook Secret
```bash
# Generate and display a secure webhook secret
php artisan telegram-bot:generate-webhook-secret

# Options:
# --length=N        Set secret length (16-128 characters, default: 64)
# --update-env      Update .env file automatically
# --force           Force overwrite existing secret without confirmation
```

### Initialize Webhook
```bash
# Set the webhook URL with Telegram
php artisan telegram-bot:init-webhook
```

## Testing

Test the module functionality:

```bash
# Test module status
curl http://localhost:8000/api/telegram-bot/test

# Test webhook (requires proper setup)
curl -X POST http://localhost:8000/api/telegram-bot/webhook \
  -H "Content-Type: application/json" \
  -d '{"update_id": 1, "message": {...}}'
```

## Troubleshooting

### Common Issues

1. **404 on webhook**: Check webhook URL configuration
2. **Unauthorized webhook**: Verify webhook secret
3. **Bot not responding**: Check bot token and permissions
4. **Database errors**: Ensure migrations are run

### Logs

Check Laravel logs for detailed error information:
```bash
tail -f storage/logs/laravel.log
```

## Support

For issues and questions, refer to:
- Laravel Modules documentation
- Telegram Bot API documentation
- irazasyed/telegram-bot-sdk documentation
