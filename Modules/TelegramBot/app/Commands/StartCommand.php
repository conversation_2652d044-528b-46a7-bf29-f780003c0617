<?php

namespace Modules\TelegramBot\Commands;

use App\Models\User;
use Modules\TelegramBot\Services\TelegramBotService;

class StartCommand implements CommandInterface
{
    protected TelegramBotService $botService;

    public function __construct(TelegramBotService $botService)
    {
        $this->botService = $botService;
    }

    /**
     * Handle the /start command
     */
    public function handle(array $message, User $user): array
    {
        $welcomeMessage = "Hello, Mate! 🤝\n\n";
        $welcomeMessage .= "🚀 Welcome to NiTon, the groundbreaking app built on Telegram!\n\n";
        $welcomeMessage .= "Explore endless possibilities with cloud mining for TON. Our platform, powered by the TON blockchain, ensures optimized transactions and lower transfer fees.\n\n";
        $welcomeMessage .= "Complete missions, invite friends, and rent additional mining power to boost your earnings even more.\n\n";
        $welcomeMessage .= "Tap to Play 👇";

        $keyboard = [
            'inline_keyboard' => [
                [
                    ['text' => 'Play & Earn 🎮', 'web_app' => ['url' => config('telegrambot.ui.mini_app_url')]]
                ],
                [
                    ['text' => 'Official Channel ✅', 'url' => config('telegrambot.ui.channel_url')]
                ],
                [
                    ['text' => 'How to Boost Miner?', 'url' => config('telegrambot.ui.how_to_boost_url')]
                ],
                [
                    ['text' => 'venuspay app', 'url' => 'https://t.me/Venuspaybot']
                ]
            ]
        ];

        $this->botService->sendMessage(
            $message['chat']['id'],
            $welcomeMessage,
            [
                'reply_markup' => json_encode($keyboard)
            ]
        );

        return ['success' => true, 'handled' => true];
    }
}
