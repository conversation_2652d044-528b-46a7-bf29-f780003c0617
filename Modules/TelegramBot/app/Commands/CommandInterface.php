<?php

namespace Modules\TelegramBot\Commands;

use App\Models\User;

interface CommandInterface
{
    /**
     * Handle the command execution
     *
     * @param array $message The Telegram message data
     * @param User $user The user who sent the command
     * @return array Response array with success status and handled flag
     */
    public function handle(array $message, User $user): array;
}
