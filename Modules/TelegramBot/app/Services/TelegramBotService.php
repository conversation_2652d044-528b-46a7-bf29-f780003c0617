<?php

namespace Modules\TelegramBot\Services;

use Illuminate\Support\Facades\Log;
use Telegram\Bot\Api;
use Telegram\Bot\Exceptions\TelegramSDKException;
use App\Services\SettingsService;

class TelegramBotService
{
    protected Api $telegram;

    public function __construct(?Api $telegram = null)
    {
        if ($telegram === null) {
            // Create our own API instance with fresh token from settings
            $settings = app(SettingsService::class)->getSettingsByKeys(['telegram_bot_token']);
            if (empty($settings['telegram_bot_token'])) {
                throw new \InvalidArgumentException('Telegram bot token is not configured');
            }
            $telegram = new Api($settings['telegram_bot_token']);
        }
        $this->telegram = $telegram;
    }

    /**
     * Get bot information
     */
    public function getBotInfo(): array
    {
        try {
            $response = $this->telegram->getMe();
            return [
                'success' => true,
                'data' => $response->toArray(),
            ];
        } catch (TelegramSDKException $e) {
            Log::error('Failed to get bot info', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Send a message to a chat
     */
    public function sendMessage(int|string $chatId, string $text, array $options = []): array
    {
        try {
            $params = array_merge([
                'chat_id' => $chatId,
                'text' => $text,
                'parse_mode' => 'HTML',
            ], $options);

            $response = $this->telegram->sendMessage($params);

            return [
                'success' => true,
                'data' => $response->toArray(),
            ];
        } catch (TelegramSDKException $e) {
            Log::error('Failed to send Telegram message', [
                'chat_id' => $chatId,
                'text' => $text,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Send a photo to a chat
     */
    public function sendPhoto(int|string $chatId, string $photo, string $caption = '', array $options = []): array
    {
        try {
            $params = array_merge([
                'chat_id' => $chatId,
                'photo' => $photo,
                'caption' => $caption,
                'parse_mode' => 'HTML',
            ], $options);

            $response = $this->telegram->sendPhoto($params);

            // Log outgoing photo if enabled
            if (config('telegrambot.logging.outgoing_messages', true)) {
                Log::info('Telegram photo sent', [
                    'chat_id' => $chatId,
                    'photo' => $photo,
                    'caption' => $caption,
                    'message_id' => $response->getMessageId(),
                ]);
            }

            return [
                'success' => true,
                'data' => $response->toArray(),
            ];
        } catch (TelegramSDKException $e) {
            Log::error('Failed to send Telegram photo', [
                'chat_id' => $chatId,
                'photo' => $photo,
                'caption' => $caption,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Edit a message
     */
    public function editMessage(int|string $chatId, int $messageId, string $text, array $options = []): array
    {
        try {
            $params = array_merge([
                'chat_id' => $chatId,
                'message_id' => $messageId,
                'text' => $text,
                'parse_mode' => 'HTML',
            ], $options);

            $response = $this->telegram->editMessageText($params);

            return [
                'success' => true,
                'data' => $response,
            ];
        } catch (TelegramSDKException $e) {
            Log::error('Failed to edit Telegram message', [
                'chat_id' => $chatId,
                'message_id' => $messageId,
                'text' => $text,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Delete a message
     */
    public function deleteMessage(int|string $chatId, int $messageId): array
    {
        try {
            $response = $this->telegram->deleteMessage([
                'chat_id' => $chatId,
                'message_id' => $messageId,
            ]);

            return [
                'success' => true,
                'data' => $response,
            ];
        } catch (TelegramSDKException $e) {
            Log::error('Failed to delete Telegram message', [
                'chat_id' => $chatId,
                'message_id' => $messageId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Answer callback query
     */
    public function answerCallbackQuery(string $callbackQueryId, string $text = '', bool $showAlert = false): array
    {
        try {
            $response = $this->telegram->answerCallbackQuery([
                'callback_query_id' => $callbackQueryId,
                'text' => $text,
                'show_alert' => $showAlert,
            ]);

            return [
                'success' => true,
                'data' => $response,
            ];
        } catch (TelegramSDKException $e) {
            Log::error('Failed to answer callback query', [
                'callback_query_id' => $callbackQueryId,
                'text' => $text,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get the underlying Telegram API instance
     */
    public function getApi(): Api
    {
        return $this->telegram;
    }
}
