<?php

namespace Modules\TelegramBot\Services;

use App\Models\User;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Modules\TelegramBot\Commands\CommandInterface;

class CommandDispatcher
{
    protected TelegramBotService $botService;

    public function __construct(TelegramBotService $botService)
    {
        $this->botService = $botService;
    }

    /**
     * Dispatch a command to its corresponding handler
     *
     * @param string $command The command name (without the /)
     * @param array $message The Telegram message data
     * @param User $user The user who sent the command
     * @return array Response array with success status and handled flag
     */
    public function dispatch(string $command, array $message, User $user): array
    {
        try {
            $className = Str::studly($command) . 'Command';
            $fqcn = "Modules\\TelegramBot\\Commands\\{$className}";

            if (!class_exists($fqcn)) {
                Log::warning('Command handler not found', [
                    'command' => $command,
                    'class' => $fqcn,
                    'user_id' => $user->tele_id,
                ]);

                // Return success but not handled for unknown commands
                return ['success' => true, 'handled' => false];
            }

            $handler = app()->make($fqcn);

            if (!$handler instanceof CommandInterface) {
                Log::error('Command handler does not implement CommandInterface', [
                    'command' => $command,
                    'class' => $fqcn,
                ]);

                return ['success' => false, 'handled' => false];
            }

            Log::info('Dispatching command', [
                'command' => $command,
                'user_id' => $user->tele_id,
                'handler' => $fqcn,
            ]);

            return $handler->handle($message, $user);

        } catch (\Exception $e) {
            Log::error('Error dispatching command', [
                'command' => $command,
                'user_id' => $user->tele_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return ['success' => false, 'handled' => false];
        }
    }
}
