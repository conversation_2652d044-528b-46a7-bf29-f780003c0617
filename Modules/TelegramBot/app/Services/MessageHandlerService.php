<?php

namespace Modules\TelegramBot\Services;

use Illuminate\Support\Facades\Log;
use App\Models\User;
use Modules\TelegramBot\Events\TelegramPreCheckoutReceived;
use Modules\TelegramBot\Events\TelegramPaymentSuccessful;

class MessageHandlerService
{
    protected TelegramBotService $botService;
    protected CommandDispatcher $commandDispatcher;

    public function __construct(CommandDispatcher $commandDispatcher)
    {
        $this->commandDispatcher = $commandDispatcher;
    }

    /**
     * Handle incoming update
     */
    public function handleUpdate(array $update, TelegramBotService $botService): array
    {
        $this->botService = $botService;

        try {
            $updateType = $this->getUpdateType($update);

            switch ($updateType) {
                case 'message':
                    return $this->handleMessage($update['message']);
                case 'callback_query':
                    return $this->handleCallbackQuery($update['callback_query']);
                case 'pre_checkout_query':
                    event(new TelegramPreCheckoutReceived($update));
                    return $this->handleSuccess();
                default:
                    Log::info('Unhandled update type', [
                        'type' => $updateType,
                        'update' => $update,
                    ]);
                    return ['success' => true, 'handled' => false];
            }
        } catch (\Exception $e) {
            Log::error('Error handling update', [
                'update' => $update,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Handle regular message
     */
    protected function handleMessage(array $message): array
    {
        $user = $this->getOrCreateUser($message['from']);

        if (isset($message['successful_payment'])) {
            event(new TelegramPaymentSuccessful(['message' => $message]));
            return $this->handleSuccess();
        }

        // Handle commands
        if (isset($message['text']) && str_starts_with($message['text'], '/')) {
            return $this->handleCommand($message, $user);
        }

        // For now, just acknowledge non-command messages
        return $this->handleSuccess();
    }

    /**
     * Handle callback query (inline keyboard button press)
     * TODO: Implement callback query handling for future development
     */
    protected function handleCallbackQuery(array $callbackQuery): array
    {
        // TODO: Implement callback query handling for inline keyboard buttons
        return $this->handleSuccess();
    }

    /**
     * Handle bot commands using CommandDispatcher
     */
    protected function handleCommand(array $message, User $user): array
    {
        $text = $message['text'];
        $command = strtok(ltrim($text, '/'), ' ');

        Log::info('Command received', [
            'user_id' => $user->tele_id,
            'command' => $command,
        ]);

        return $this->commandDispatcher->dispatch($command, $message, $user);
    }

    /**
     * Get or create user from Telegram user data
     */
    protected function getOrCreateUser(array $telegramUser): User
    {
        return User::updateOrCreate(
            ['tele_id' => (string) $telegramUser['id']],
            [
                'name' => $telegramUser['first_name'] ?? $telegramUser['username'] ?? 'Unknown',
                'username' => $telegramUser['username'] ?? null,
                'language_code' => $telegramUser['language_code'] ?? null,
                'last_active' => now(),
            ]
        );
    }

    /**
     * Determine update type
     */
    protected function getUpdateType(array $update): string
    {
        $types = [
            'message',
            'callback_query',
            'pre_checkout_query'
        ];

        foreach ($types as $type) {
            if (isset($update[$type])) {
                return $type;
            }
        }

        return 'unknown';
    }

    protected function handleSuccess($data = [])
    {
        return [
            'success' => true,
            'handled' => true,
            'data' => $data,
        ];
    }
}
