<?php

namespace Modules\TelegramBot\Console;

use Illuminate\Console\Command;
use Modules\TelegramClient\Services\TelegramBotClient;

class InitTelegramWebhook extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'telegram-bot:init-webhook';

    /**
     * The console command description.
     */
    protected $description = 'Initialize Telegram webhook for the bot.';

    /**
     * Create a new command instance.
     */
    public function __construct()
    {
        parent::__construct();
    }

    public function handle() {
        $client = app(TelegramBotClient::class);

        $appUrl = env('APP_URL') . '/api/telegram-bot/webhook';
        if (!$appUrl) {
            throw new \InvalidArgumentException('APP_URL is not configured');
        }

        $webhookSecret = env('TELEGRAM_WEBHOOK_SECRET');
        if (!$webhookSecret) {
            throw new \InvalidArgumentException('TELEGRAM_WEBHOOK_SECRET is not configured. please run "php artisan telegram-bot:generate-webhook-secret"');
        }

        $response = $client->setWebhookUrl($appUrl, $webhookSecret);

        if (isset($response['ok']) && $response['ok']) {
            $this->info('Webhook was set successfully to: ' . $appUrl);
            $this->info('Webhook log: ' . json_encode($response));
        } else {
            $this->error('Failed to set webhook: ' . ($response['description'] ?? 'Unknown error'));
        }
    }
}
