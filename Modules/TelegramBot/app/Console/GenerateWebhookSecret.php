<?php

namespace Modules\TelegramBot\Console;

use Illuminate\Console\Command;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\File;

class GenerateWebhookSecret extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'telegram-bot:generate-webhook-secret
                            {--length=64 : The length of the generated secret (16-128 characters)}
                            {--update-env : Update the .env file with the generated secret}
                            {--force : Force overwrite existing TELEGRAM_WEBHOOK_SECRET in .env}
                            {--show : Display the generated secret (default behavior)}';

    /**
     * The console command description.
     */
    protected $description = 'Generate a cryptographically secure random string for TELEGRAM_WEBHOOK_SECRET';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        try {
            // Validate length parameter
            $length = $this->option('length');
            if (!$this->validateLength($length)) {
                return Command::FAILURE;
            }

            // Generate secure random secret
            $secret = $this->generateSecureSecret($length);

            // Handle .env file update if requested
            if ($this->option('update-env')) {
                if (!$this->updateEnvFile($secret)) {
                    return Command::FAILURE;
                }
            } else {
                // Display the secret for manual addition
                $this->displaySecret($secret);
            }

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error('An error occurred while generating the webhook secret: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * Validate the length parameter.
     */
    private function validateLength(int $length): bool
    {
        if ($length < 16 || $length > 128) {
            $this->error('Secret length must be between 16 and 128 characters.');
            return false;
        }

        return true;
    }

    /**
     * Generate a cryptographically secure random secret.
     */
    private function generateSecureSecret(int $length): string
    {
        // Use Laravel's secure random string generator
        // This uses random_bytes() internally which is cryptographically secure
        return Str::random($length);
    }

    /**
     * Display the generated secret to the user.
     */
    private function displaySecret(string $secret): void
    {
        $this->info('Generated TELEGRAM_WEBHOOK_SECRET:');
        $this->line('');
        $this->line('TELEGRAM_WEBHOOK_SECRET=' . $secret);
        $this->line('');
        $this->comment('Please add this line to your .env file manually, or run the command with --update-env to update automatically.');
        $this->line('');
        $this->warn('Keep this secret secure and do not share it publicly!');
    }

    /**
     * Update the .env file with the generated secret.
     */
    private function updateEnvFile(string $secret): bool
    {
        $envPath = base_path('.env');

        // Check if .env file exists
        if (!File::exists($envPath)) {
            $this->error('.env file not found. Please create it first.');
            return false;
        }

        // Check if .env file is writable
        if (!File::isWritable($envPath)) {
            $this->error('.env file is not writable. Please check file permissions.');
            return false;
        }

        try {
            $envContent = File::get($envPath);
            $secretKey = 'TELEGRAM_WEBHOOK_SECRET';
            $newLine = $secretKey . '=' . $secret;

            // Check if TELEGRAM_WEBHOOK_SECRET already exists
            if (preg_match('/^' . preg_quote($secretKey) . '=.*$/m', $envContent)) {
                if (!$this->option('force')) {
                    if (!$this->confirm('TELEGRAM_WEBHOOK_SECRET already exists in .env file. Do you want to overwrite it?')) {
                        $this->info('Operation cancelled.');
                        return false;
                    }
                }

                // Replace existing value
                $envContent = preg_replace(
                    '/^' . preg_quote($secretKey) . '=.*$/m',
                    $newLine,
                    $envContent
                );
                $this->info('Updated existing TELEGRAM_WEBHOOK_SECRET in .env file.');
            } else {
                // Append new value
                $envContent = rtrim($envContent) . "\n" . $newLine . "\n";
                $this->info('Added TELEGRAM_WEBHOOK_SECRET to .env file.');
            }

            // Write back to file
            File::put($envPath, $envContent);

            $this->line('');
            $this->info('✓ TELEGRAM_WEBHOOK_SECRET has been successfully updated in .env file.');
            $this->comment('Generated secret: ' . $secret);
            $this->line('');
            $this->warn('Keep this secret secure and do not share it publicly!');
            $this->line('');
            $this->comment('You may need to restart your application for the changes to take effect.');

            return true;

        } catch (\Exception $e) {
            $this->error('Failed to update .env file: ' . $e->getMessage());
            return false;
        }
    }
}
