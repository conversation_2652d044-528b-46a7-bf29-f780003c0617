<?php

namespace Modules\TelegramBot\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use App\Models\User;
use Modules\TelegramBot\Services\TelegramBotService;

class TelegramBotController extends Controller
{
    protected TelegramBotService $botService;

    public function __construct(TelegramBotService $botService)
    {
        $this->botService = $botService;
    }

    /**
     * Get bot information
     */
    public function getBotInfo(): JsonResponse
    {
        try {
            $result = $this->botService->getBotInfo();

            return response()->json([
                'success' => $result['success'],
                'data' => $result['data'] ?? null,
                'error' => $result['error'] ?? null,
            ], $result['success'] ? 200 : 500);
        } catch (\Exception $e) {
            Log::error('Failed to get bot info', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Send a test message
     */
    public function sendTestMessage(Request $request): JsonResponse
    {
        $request->validate([
            'chat_id' => 'required|integer',
            'message' => 'required|string|max:4096',
        ]);

        try {
            $result = $this->botService->sendMessage(
                $request->input('chat_id'),
                $request->input('message')
            );

            return response()->json([
                'success' => $result['success'],
                'data' => $result['data'] ?? null,
                'error' => $result['error'] ?? null,
            ], $result['success'] ? 200 : 500);
        } catch (\Exception $e) {
            Log::error('Failed to send test message', [
                'chat_id' => $request->input('chat_id'),
                'message' => $request->input('message'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get bot statistics
     */
    public function getStatistics(): JsonResponse
    {
        try {
            $stats = [
                'total_users' => User::whereNotNull('tele_id')->count(),
                'active_users' => User::whereNotNull('tele_id')->active()->count(),
                'real_users' => User::whereNotNull('tele_id')->realUsers()->count(),
                'new_users_today' => User::whereNotNull('tele_id')->whereDate('created_at', today())->count(),
                'message_processing' => 'Real-time processing enabled',
                'message_storage' => 'Disabled - messages not stored in database',
            ];

            return response()->json([
                'success' => true,
                'data' => $stats,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get bot statistics', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get users list
     */
    public function getUsers(Request $request): JsonResponse
    {
        try {
            $query = User::whereNotNull('tele_id');

            // Filter by status
            if ($request->has('status')) {
                switch ($request->input('status')) {
                    case 'active':
                        $query->active();
                        break;
                    case 'blocked':
                        $query->where('is_blocked', true);
                        break;
                    case 'real':
                        $query->realUsers();
                        break;
                }
            }

            // Search by username or name
            if ($request->has('search')) {
                $search = $request->input('search');
                $query->where(function ($q) use ($search) {
                    $q->where('username', 'like', "%{$search}%")
                      ->orWhere('name', 'like', "%{$search}%")
                      ->orWhere('tele_id', 'like', "%{$search}%");
                });
            }

            $users = $query->orderBy('last_active', 'desc')
            ->paginate($request->input('per_page', 20));

            return response()->json([
                'success' => true,
                'data' => $users,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get users list', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get user details
     */
    public function getUserDetails(Request $request): JsonResponse
    {
        $request->validate([
            'telegram_id' => 'required|string',
        ]);

        try {
            $user = User::where('tele_id', $request->input('telegram_id'))
                       ->first();

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'error' => 'User not found',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'user' => $user,
                    'stats' => [
                        'message_processing' => 'Real-time processing enabled',
                        'message_storage' => 'Disabled - messages not stored in database',
                        'last_active' => $user->last_active,
                    ],
                ],
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get user details', [
                'telegram_id' => $request->input('telegram_id'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
