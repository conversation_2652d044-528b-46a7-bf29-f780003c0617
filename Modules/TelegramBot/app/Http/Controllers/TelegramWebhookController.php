<?php

namespace Modules\TelegramBot\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;
use Modules\TelegramBot\Models\TelegramWebhookLog;
use Modules\TelegramBot\Services\WebhookService;
use Modules\TelegramBot\Services\MessageHandlerService;
use Modules\TelegramBot\Services\TelegramBotService;

class TelegramWebhookController extends Controller
{
    protected WebhookService $webhookService;
    protected MessageHandlerService $messageHandler;
    protected TelegramBotService $botService;

    public function __construct(
        WebhookService $webhookService,
        MessageHandlerService $messageHandler,
        TelegramBotService $botService
    ) {
        $this->webhookService = $webhookService;
        $this->messageHandler = $messageHandler;
        $this->botService = $botService;
    }

    /**
     * Handle incoming webhook from Telegram
     */
    public function handleWebhook(Request $request): Response
    {
        if ($request->isMethod('GET')) {
            return response('OK', 200);
        }

        $webhookLog = null;

        try {
            $verificationResult = $this->webhookService->verifyWebhookRequest($request);

            if (!$verificationResult) {
                Log::warning('Webhook verification failed', [
                    'ip' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                ]);
                return response('Unauthorized', 401);
            }

            $payload = $request->all();

            $webhookLog = TelegramWebhookLog::create([
                'update_id' => $payload['update_id'] ?? null,
                'update_type' => $this->webhookService->getUpdateType($payload),
                'payload' => $payload,
                'status' => 'pending',
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            $handleResult = $this->messageHandler->handleUpdate($payload, $this->botService);

            if (!$handleResult['success']) {
                $webhookLog->markAsFailed($handleResult['error']);
                return response('Error handling update', 500);
            }

            $webhookLog->markAsProcessed();

            return response('OK', 200);

        } catch (\Exception $e) {
            Log::error('Webhook processing error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'payload' => $request->all(),
            ]);

            if ($webhookLog) {
                $webhookLog->markAsFailed($e->getMessage());
            }

            // Always return 200 to prevent Telegram from retrying
            return response('OK', 200);
        }
    }
}
