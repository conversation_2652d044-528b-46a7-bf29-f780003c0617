<?php

return [
    'name' => 'TelegramTonPayment',

    /*
    |--------------------------------------------------------------------------
    | TON Blockchain Configuration
    |--------------------------------------------------------------------------
    */
    'ton_api_url' => env('TON_API_URL'),
    'ton_api_key' => env('TON_API_KEY'),
    'ton_receiver_address' => env('TON_RECEIVER_ADDRESS'),

    /*
    |--------------------------------------------------------------------------
    | Payment Configuration
    |--------------------------------------------------------------------------
    */
    'max_ton_amount' => 100.0,     // Maximum TON per transaction
    'min_ton_amount' => 0.001,     // Minimum TON per transaction
    'transaction_timeout_minutes' => 5, // Transaction validity window

    /*
    |--------------------------------------------------------------------------
    | Message Configuration
    |--------------------------------------------------------------------------
    */
    'message_prefix' => 'miner_sc_', // Prefix for transaction messages
];
