<?php

use Illuminate\Support\Facades\Route;
use Modules\TelegramTonPayment\Http\Controllers\TelegramTonPaymentController;

/*
|--------------------------------------------------------------------------
| Telegram TON Payment API Routes
|--------------------------------------------------------------------------
*/

Route::middleware(['user.auth'])->prefix('ton-payment')->group(function () {
    Route::post('/create-message', [TelegramTonPaymentController::class, 'createPaymentMessage']);
    Route::post('/complete', [TelegramTonPaymentController::class, 'completePayment']);
});

// Public routes
Route::get('/ton-price', [TelegramTonPaymentController::class, 'getTonPrice']);
