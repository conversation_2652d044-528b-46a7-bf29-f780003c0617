<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ton_transactions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->string('transaction_hash', 64)->unique();
            $table->bigInteger('ton_amount_nano'); // Amount in nanoTON
            $table->decimal('ton_amount_decimal', 20, 9); // Amount in TON (decimal)
            $table->string('message'); // Transaction message for blockchain
            $table->string('receiver_address'); // TON wallet address
            $table->string('status', 20)->default('pending'); // pending, completed, failed
            $table->string('blockchain_hash')->nullable(); // Actual blockchain transaction hash
            $table->text('description')->nullable();
            $table->json('metadata')->nullable(); // Additional data
            $table->timestamps();

            // Indexes for performance
            $table->index('user_id');
            $table->index('transaction_hash');
            $table->index('status');
            $table->index(['user_id', 'status']);
            $table->index(['status', 'created_at']);

            // Foreign key constraint
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ton_transactions');
    }
};
