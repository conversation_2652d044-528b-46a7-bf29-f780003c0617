<?php

namespace Modules\TelegramTonPayment\Services;

use App\Enums\TransactionStatus;
use App\Models\User;
use Modules\TelegramTonPayment\Models\TonTransaction;
use Modules\TelegramTonPayment\Events\AfterTonPaymentSucceeded;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class TonPaymentService
{
    private TonBlockchainService $blockchainService;

    public function __construct(TonBlockchainService $blockchainService)
    {
        $this->blockchainService = $blockchainService;
    }

    /**
     * Create or retrieve existing TON payment transaction
     */
    public function createPaymentTransaction(User $user, float $tonAmount): TonTransaction
    {
        $nanoTon = $tonAmount * 1000_000_000;

        // Check for existing pending transaction within timeout window
        $timeoutMinutes = config('telegramtonpayment.transaction_timeout_minutes', 5);
        $existingTransaction = TonTransaction::where('user_id', $user->id)
            ->where('ton_amount_nano', $nanoTon)
            ->pending()
            ->recent($timeoutMinutes)
            ->first();

        if ($existingTransaction) {
            // Clean up other pending transactions for this user
            TonTransaction::where('user_id', $user->id)
                ->pending()
                ->where('id', '!=', $existingTransaction->id)
                ->delete();

            return $existingTransaction;
        }

        // Create new transaction
        $transactionHash = Str::random(32);
        $message = $this->blockchainService->buildMessage($transactionHash);
        $receiverAddress = config('telegramtonpayment.ton_receiver_address');

        return TonTransaction::create([
            'user_id' => $user->id,
            'transaction_hash' => $transactionHash,
            'ton_amount_nano' => $nanoTon,
            'ton_amount_decimal' => $tonAmount,
            'message' => $message,
            'receiver_address' => $receiverAddress,
            'status' => TransactionStatus::PENDING,
            'description' => 'TON payment for boost',
        ]);
    }

    /**
     * Complete a TON payment transaction
     */
    public function completePaymentTransaction(User $user, string $transactionHash): array
    {
        $transaction = TonTransaction::where('user_id', $user->id)
            ->where('transaction_hash', $transactionHash)
            ->pending()
            ->first();

        if (!$transaction) {
            return [
                'success' => false,
                'message' => 'No pending transaction found',
                'code' => 404
            ];
        }

        // Validate transaction on blockchain
        $isValid = $this->blockchainService->validateTransactionByMessage(
            $transaction->receiver_address,
            $transaction->message,
            $transaction->ton_amount_nano
        );

        if (!$isValid) {
            return [
                'success' => false,
                'message' => 'Transaction not found on blockchain or failed',
                'code' => 400,
                'data' => [
                    'transaction_hash' => $transaction->transaction_hash,
                    'status' => $transaction->status,
                ]
            ];
        }

        try {
            DB::beginTransaction();

            // Mark transaction as completed
            $transaction->markAsCompleted();

            // Fire event for post-processing
            event(new AfterTonPaymentSucceeded($transaction->id));

            DB::commit();

            return [
                'success' => true,
                'message' => 'Transaction completed successfully',
                'code' => 200,
                'data' => [
                    'transaction_hash' => $transaction->transaction_hash,
                    'status' => $transaction->status,
                    'ton_amount' => $transaction->ton_amount_decimal,
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to complete TON payment transaction', [
                'transaction_id' => $transaction->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Transaction confirmed on blockchain, but failed to process',
                'code' => 500
            ];
        }
    }

    /**
     * Validate TON amount against configured limits
     */
    public function validateTonAmount(float $tonAmount): array
    {
        $minAmount = config('telegramtonpayment.min_ton_amount', 0.001);
        $maxAmount = config('telegramtonpayment.max_ton_amount', 100.0);

        if ($tonAmount < $minAmount) {
            return [
                'valid' => false,
                'message' => "Minimum TON amount is {$minAmount}",
            ];
        }

        if ($tonAmount > $maxAmount) {
            return [
                'valid' => false,
                'message' => "Maximum TON amount is {$maxAmount}",
            ];
        }

        return ['valid' => true];
    }

    /**
     * Get user's recent TON transactions
     */
    public function getUserTransactions(User $user, int $limit = 10): \Illuminate\Database\Eloquent\Collection
    {
        return TonTransaction::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Clean up old pending transactions
     */
    public function cleanupOldTransactions(): int
    {
        $timeoutMinutes = config('telegramtonpayment.transaction_timeout_minutes', 5);

        return TonTransaction::pending()
            ->where('created_at', '<', now()->subMinutes($timeoutMinutes * 2))
            ->delete();
    }
}
