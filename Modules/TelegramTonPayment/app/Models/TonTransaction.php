<?php

namespace Modules\TelegramTonPayment\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\User;
use App\Enums\TransactionStatus;

class TonTransaction extends Model
{
    use HasFactory;

    protected $table = 'ton_transactions';

    protected $fillable = [
        'user_id',
        'transaction_hash',
        'ton_amount_nano',
        'ton_amount_decimal',
        'message',
        'receiver_address',
        'status',
        'blockchain_hash',
        'description',
        'metadata',
    ];

    protected $casts = [
        'status' => TransactionStatus::class,
        'ton_amount_nano' => 'integer',
        'ton_amount_decimal' => 'decimal:9',
        'metadata' => 'array',
    ];

    /**
     * Get the user that owns the transaction
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope for pending transactions
     */
    public function scopePending($query)
    {
        return $query->where('status', TransactionStatus::PENDING);
    }

    /**
     * Scope for completed transactions
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', TransactionStatus::COMPLETED);
    }

    /**
     * Scope for recent transactions (within specified minutes)
     */
    public function scopeRecent($query, $minutes = 5)
    {
        return $query->where('created_at', '>=', now()->subMinutes($minutes));
    }

    /**
     * Check if transaction is pending
     */
    public function isPending(): bool
    {
        return $this->status === TransactionStatus::PENDING;
    }

    /**
     * Check if transaction is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === TransactionStatus::COMPLETED;
    }

    /**
     * Mark transaction as completed
     */
    public function markAsCompleted(?string $blockchainHash = null): void
    {
        $this->update([
            'status' => TransactionStatus::COMPLETED,
            'blockchain_hash' => $blockchainHash,
        ]);
    }
}
