<?php

namespace Modules\TelegramTonPayment\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CreateTonPaymentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $minAmount = config('telegramtonpayment.min_ton_amount', 0.001);
        $maxAmount = config('telegramtonpayment.max_ton_amount', 100.0);

        return [
            'ton' => [
                'required',
                'numeric',
                "min:{$minAmount}",
                "max:{$maxAmount}",
            ],
        ];
    }

    /**
     * Get custom error messages for validation rules.
     */
    public function messages(): array
    {
        $minAmount = config('telegramtonpayment.min_ton_amount', 0.001);
        $maxAmount = config('telegramtonpayment.max_ton_amount', 100.0);

        return [
            'ton.required' => 'TON amount is required.',
            'ton.numeric' => 'TON amount must be a valid number.',
            'ton.min' => "Minimum TON amount is {$minAmount}.",
            'ton.max' => "Maximum TON amount is {$maxAmount}.",
        ];
    }
}
