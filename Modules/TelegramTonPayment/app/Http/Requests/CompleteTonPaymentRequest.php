<?php

namespace Modules\TelegramTonPayment\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CompleteTonPaymentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'hash' => [
                'required',
                'string',
                'size:32', // Transaction hash should be 32 characters
            ],
        ];
    }

    /**
     * Get custom error messages for validation rules.
     */
    public function messages(): array
    {
        return [
            'hash.required' => 'Transaction hash is required.',
            'hash.string' => 'Transaction hash must be a string.',
            'hash.size' => 'Transaction hash must be exactly 32 characters.',
        ];
    }
}
