<?php

namespace Modules\TelegramTonPayment\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\TelegramTonPayment\Http\Requests\CreateTonPaymentRequest;
use Modules\TelegramTonPayment\Http\Requests\CompleteTonPaymentRequest;
use Modules\TelegramTonPayment\Http\Resources\TonTransactionResource;
use Modules\TelegramTonPayment\Services\TonPaymentService;
use Modules\TelegramTonPayment\Services\TonBlockchainService;

class TelegramTonPaymentController extends Controller
{
    private TonPaymentService $tonPaymentService;
    private TonBlockchainService $blockchainService;

    public function __construct(TonPaymentService $tonPaymentService, TonBlockchainService $blockchainService)
    {
        $this->tonPaymentService = $tonPaymentService;
        $this->blockchainService = $blockchainService;
    }

    /**
     * Create a TON payment message
     */
    public function createPaymentMessage(CreateTonPaymentRequest $request): JsonResponse
    {
        $user = User::find($request->user()->id);
        $tonAmount = $request->input('ton');

        // Validate TON amount
        $validation = $this->tonPaymentService->validateTonAmount($tonAmount);
        if (!$validation['valid']) {
            return response()->json([
                'success' => false,
                'message' => $validation['message'],
            ], 422);
        }

        try {
            $transaction = $this->tonPaymentService->createPaymentTransaction($user, $tonAmount);

            return response()->json([
                'success' => true,
                'message' => 'Payment message created successfully',
                'data' => new TonTransactionResource($transaction),
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create payment message',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Complete a TON payment transaction
     */
    public function completePayment(CompleteTonPaymentRequest $request): JsonResponse
    {
        $user = User::find($request->user()->id);
        $transactionHash = $request->input('hash');

        $result = $this->tonPaymentService->completePaymentTransaction($user, $transactionHash);

        return response()->json([
            'success' => $result['success'],
            'message' => $result['message'],
            'data' => $result['data'] ?? null,
        ], $result['code']);
    }

    /**
     * Get current TON price
     */
    public function getTonPrice(): JsonResponse
    {
        try {
            $price = $this->blockchainService->fetchTonPrice();

            if ($price === null) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unable to fetch TON price',
                ], 503);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'ton_price_usd' => $price,
                    'timestamp' => now()->toISOString(),
                ],
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching TON price',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
