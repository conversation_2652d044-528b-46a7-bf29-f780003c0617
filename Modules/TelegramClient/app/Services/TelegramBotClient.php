<?php

namespace Modules\TelegramClient\Services;

use App\Models\User;
use App\Services\SettingsService;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class TelegramBotClient
{
    protected $baseUrl;

    public function __construct()
    {
        $settings = app(SettingsService::class)->getSettingsByKeys(['telegram_bot_token']);
        if (empty($settings['telegram_bot_token'])) {
            throw new \InvalidArgumentException('Telegram bot token is not configured');
        }
        $this->baseUrl = 'https://api.telegram.org/bot' . ($settings['telegram_bot_token'] ?? '') . '/';
    }

    public function setWebhookUrl($url, $secret) {
        $response = Http::timeout(30)->get($this->baseUrl . 'setWebhook', [
            'url' => $url,
            'secret_token' => $secret
        ]);
        return $response->json();
    }

    /**
     * Create a Telegram Stars invoice link
     *
     * @param User $user The user for whom the invoice is being created
     * @param array $data Invoice data containing title, description, payload, currency, prices, etc.
     * @return array Response containing invoice link or error information
     */
    public function createInvoiceLink(array $data): array
    {
        try {
            $requiredFields = ['title', 'description', 'payload', 'prices'];
            foreach ($requiredFields as $field) {
                if (!isset($data[$field]) || empty($data[$field])) {
                    return [
                        'success' => false,
                        'message' => "Missing required field: {$field}",
                    ];
                }
            }

            // Prepare the request payload
            $payload = [
                'title' => $data['title'],
                'description' => $data['description'],
                'payload' => $data['payload'],
                'provider_token' => '',
                'currency' => 'XTR',
                'prices' => $data['prices'],
            ];

            // Add optional fields if provided
            $optionalFields = [
                'max_tip_amount',
                'suggested_tip_amounts',
                'start_parameter',
                'provider_data',
                'photo_url',
                'photo_size',
                'photo_width',
                'photo_height',
                'need_name',
                'need_phone_number',
                'need_email',
                'need_shipping_address',
                'send_phone_number_to_provider',
                'send_email_to_provider',
                'is_flexible',
            ];

            foreach ($optionalFields as $field) {
                if (isset($data[$field])) {
                    $payload[$field] = $data[$field];
                }
            }

            // Make the API request to Telegram
            $response = Http::timeout(30)
                ->withHeaders([
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                ])
                ->post($this->baseUrl . 'createInvoiceLink', $payload);

            // Check if the request was successful
            if (!$response->successful()) {
                Log::error('Telegram createInvoiceLink API error', [
                    'status' => $response->status(),
                    'response' => $response->body(),
                    'payload' => $payload,
                ]);

                return [
                    'success' => false,
                    'message' => 'Failed to create invoice link',
                    'error' => $response->json()['description'] ?? 'Unknown error',
                ];
            }

            $responseData = $response->json();

            // Check if Telegram API returned success
            if (!$responseData['ok']) {
                Log::error('Telegram createInvoiceLink API returned error', [
                    'response' => $responseData,
                    'payload' => $payload,
                ]);

                return [
                    'success' => false,
                    'message' => 'Telegram API error',
                    'error' => $responseData['description'] ?? 'Unknown error',
                ];
            }

            return [
                'success' => true,
                'invoice_link' => $responseData['result'],
            ];

        } catch (\Exception $e) {
            Log::error('Exception in createInvoiceLink', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data,
            ]);

            return [
                'success' => false,
                'message' => 'An error occurred while creating the invoice link',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Answer a pre-checkout query
     *
     * @param string $preCheckoutQueryId The pre-checkout query ID to answer
     * @return array Response containing success status or error information
     */
    public function answerPreCheckoutQuery(string $preCheckoutQueryId, bool $ok): array
    {
        $payload = [
            'pre_checkout_query_id' => $preCheckoutQueryId,
            'ok' => $ok,
        ];

        // Make the API request to Telegram
        $response = Http::timeout(30)
            ->withHeaders([
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ])
            ->post($this->baseUrl . 'answerPreCheckoutQuery', $payload);

        // Check if the request was successful
        if (!$response->successful()) {
            Log::error('Telegram answerPreCheckoutQuery API error', [
                'status' => $response->status(),
                'response' => $response->body(),
                'payload' => $payload,
            ]);

            throw new \Exception('Failed to answer pre-checkout query: ' . ($response->json()['description'] ?? 'Unknown error'));
        }

        $responseData = $response->json();

        // Check if Telegram API returned success
        if (!$responseData['ok']) {
            Log::error('Telegram answerPreCheckoutQuery API returned error', [
                'response' => $responseData,
                'payload' => $payload,
            ]);

            throw new \Exception('Telegram answerPreCheckoutQuery API returned error: ' . ($responseData['description'] ?? 'Unknown error'));
        }

        return [
            'success' => true,
            'result' => $responseData['result'],
        ];
}
}
