# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Testing
```bash
# Run all tests
php artisan test

# Run all tests (alternative via composer)
composer test

# Run specific test file
php artisan test tests/Feature/ReferralSystemTest.php

# Run tests with coverage
php artisan test --coverage
```

### Code Quality
```bash
# Run Laravel Pint (code formatting)
vendor/bin/pint

# Check code style without fixing
vendor/bin/pint --test
```

### Development Server
```bash
# Start development server
php artisan serve

# Alternative via composer
composer dev

# Using Docker
docker-compose up -d
```

### Database
```bash
# Run migrations
php artisan migrate

# Run migrations with Docker
docker-compose exec backend php artisan migrate

# Reset and re-run migrations
php artisan migrate:fresh

# Seed database
php artisan db:seed
```

### Cache & Config
```bash
# Clear application cache
php artisan cache:clear

# Clear config cache
php artisan config:clear

# Clear compiled views
php artisan view:clear

# Clear all caches
php artisan optimize:clear
```

## Architecture Overview

This is a **Laravel 12 API backend** for a cryptocurrency mining application with Telegram integration. The system handles user management, TON blockchain payments, referral systems, and admin functionality.

### Core Components

#### Authentication System
- **User Authentication**: Custom middleware (`UserAuth`) for Telegram-based user auth
- **Staff Authentication**: Token-based authentication for staff members
- **Admin Authentication**: Custom middleware (`AdminAuth`) for admin access
- Uses Laravel Sanctum for token management

#### Key Models & Relationships
- **User**: Telegram users with TON/token balances, referral codes
- **Staff**: Administrative staff with authentication tokens
- **Wallet**: User cryptocurrency wallets
- **Transaction**: Financial transactions and payments
- **Refs**: Referral relationships between users
- **Blocked**: User blocking system

#### API Structure
- **Public Routes** (`/api/public/*`): Health checks, info, referral validation
- **User Routes** (`/api/user/*`): TON payments, referral management (requires user auth)
- **Staff Routes** (`/api/staff/*`): Staff authentication and management
- **Admin Routes** (`/api/admin/*`): User management, blocking/unblocking

#### Payment System
- **TON Integration**: TON blockchain payment processing via `TonPaymentController`
- **Transaction Lifecycle**: init → complete/cancel workflow
- **Balance Management**: Separate TON and token balances per user

#### Referral System
- **Referral Codes**: 6-character alphanumeric codes (excluding confusing characters)
- **Referral Links**: Generated links for user sharing
- **Commission Tracking**: Tracked via `Refs` model
- **Validation**: Public endpoint for validating referral codes during registration

### Key Services
- **ReferralService**: Handles referral code generation, validation, and statistics
- **TonHelper**: Utilities for TON blockchain operations

### Database Schema
- **PostgreSQL/MariaDB** compatible
- **Decimal precision**: 20,8 for cryptocurrency amounts
- **Telegram Integration**: `tele_id` field for user identification
- **Referral Tracking**: Self-referencing relationships in `Refs` table

### Docker Setup
- **FrankenPHP**: High-performance PHP server
- **MariaDB**: Database with phpMyAdmin interface
- **Redis**: Caching and session storage
- **Multi-container**: Separate services for backend, database, cache

### Testing Strategy
- **Feature Tests**: API endpoint testing with authentication
- **Unit Tests**: Business logic and model methods
- **Test Database**: In-memory SQLite for testing
- **Key Test Files**: `ReferralSystemTest.php`, `StaffAuthTest.php`, `UserSyncTest.php`

### Security Considerations
- **Input Validation**: Comprehensive validation rules
- **Authentication Guards**: Multiple auth guards for different user types
- **Rate Limiting**: Built-in Laravel rate limiting
- **CORS**: Configured for cross-origin requests
- **User Blocking**: Admin-controlled user blocking system

## Development Notes

### Working with Referrals
- Referral codes are auto-generated and stored in `users.referral_code`
- Use `User::validateReferralCode()` for validation
- Referral relationships are tracked in the `refs` table

### TON Payment Flow
1. User initiates payment via `/api/user/ton-payment/init`
2. System creates transaction record
3. User completes payment via `/api/user/ton-payment/complete`
4. Balance is updated automatically

### Admin Operations
- User blocking/unblocking via `/api/admin/users/block` and `/api/admin/users/unblock`
- Staff authentication via `/api/staff/auth/login`
- All admin routes require `admin.auth` middleware

### Docker Development
- Use `docker-compose up -d` for full stack
- Backend runs on port 8000
- Database accessible via phpMyAdmin on port 8080
- Redis available on port 6380

### Code Style
- Uses Laravel Pint for code formatting
- PSR-12 coding standards
- Comprehensive PHPDoc comments
- Consistent API response format

### Docker Command Guidelines
- **Always execute php artisan command inside docker instead**
```

</invoke>