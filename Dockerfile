FROM dunglas/frankenphp

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    zip \
    unzip \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install PHP extensions
RUN install-php-extensions \
    pcntl \
    redis \
    pdo_mysql \
    mysqli \
    zip
    # Add other PHP extensions here...

# Create user with same UID/GID as host user
ARG USER_ID=1000
ARG GROUP_ID=1000
RUN groupadd -g ${GROUP_ID} appuser && \
    useradd -u ${USER_ID} -g appuser -m -s /bin/bash appuser

# Install Composer
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

# Set working directory and permissions
WORKDIR /app
RUN chown -R appuser:appuser /app

# Copy application files
COPY --chown=appuser:appuser . /app

# Switch to non-root user
USER appuser

ENTRYPOINT ["php", "artisan", "octane:frankenphp"]
