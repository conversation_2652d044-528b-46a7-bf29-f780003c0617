#!/bin/bash

# Fix Docker Permissions Script
# This script fixes file permissions for files created by Docker containers

echo "🔧 Fixing Docker file permissions..."

# Fix permissions for all files that might be owned by root
echo "📁 Fixing config directory permissions..."
docker-compose exec --user root backend chown -R 1000:1000 /app/config

echo "📁 Fixing Modules directory permissions..."
docker-compose exec --user root backend chown -R 1000:1000 /app/Modules

echo "📁 Fixing stubs directory permissions..."
docker-compose exec --user root backend chown -R 1000:1000 /app/stubs

echo "📁 Fixing storage directory permissions..."
docker-compose exec --user root backend chown -R 1000:1000 /app/storage

echo "📁 Fixing bootstrap/cache directory permissions..."
docker-compose exec --user root backend chown -R 1000:1000 /app/bootstrap/cache

echo "🔍 Finding and fixing any remaining root-owned files..."
docker-compose exec --user root backend find /app -user root -exec chown 1000:1000 {} \;

echo "✅ All permissions fixed!"
echo "📝 All files should now be editable from your IDE."

# Make the script executable
chmod +x fix-permissions.sh
