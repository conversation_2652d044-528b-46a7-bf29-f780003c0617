<?php

namespace Database\Seeders;

use App\Enums\TransactionStatus;
use App\Enums\TransactionType;
use App\Models\Transaction;
use App\Models\User;
use Illuminate\Database\Seeder;

class TransactionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Find or create the johndoe user
        $user = $this->findOrCreateJohnDoeUser();

        // Generate realistic transaction data for johndoe
        $this->createTransactionsForJohnDoe($user);

        $this->command->info("Created transactions for user: {$user->username} (ID: {$user->id})");
    }

    /**
     * Find or create the johndoe user
     */
    private function findOrCreateJohnDoeUser(): User
    {
        $user = User::where('username', 'johndoe')->first();

        if (!$user) {
            $user = User::create([
                'name' => 'John Doe',
                'username' => 'johndoe',
                'tele_id' => '999888777',
                'country' => 'US',
                'language_code' => 'en',
                'balance_ton' => 25.75,
                'balance_token' => 3500.00,
                'speed' => 4.25,
                'pending_token' => 225.50,
                'last_active' => now()->subMinutes(30),
                'last_checkpoint' => now()->subMinutes(15),
                'avatar_url' => 'https://api.dicebear.com/7.x/avataaars/svg?seed=johndoe',
            ]);

            // Generate referral code
            $user->generateReferralCode();

            $this->command->info("Created new user: johndoe");
        } else {
            $this->command->info("Found existing user: johndoe");
        }

        return $user;
    }

    /**
     * Create realistic transactions for johndoe user
     */
    private function createTransactionsForJohnDoe(User $user): void
    {
        // Clear existing transactions for johndoe to avoid duplicates
        Transaction::where('user_id', $user->id)->delete();

        $transactions = [
            // Recent boost payment
            [
                'type' => TransactionType::BOOST_PAYMENT,
                'value' => 2.50,
                'status' => TransactionStatus::COMPLETED,
                'description' => 'Purchase of Speed Boost for 2.50 TON',
                'created_at' => now()->subHours(2),
            ],
            // Referral bonus from yesterday
            [
                'type' => TransactionType::REFERRAL_BONUS,
                'value' => 0.75,
                'status' => TransactionStatus::COMPLETED,
                'description' => 'Referral bonus earned from successful referral',
                'created_at' => now()->subDay(),
            ],
            // Withdrawal from 3 days ago
            [
                'type' => TransactionType::WITHDRAWAL,
                'value' => 15.00,
                'status' => TransactionStatus::COMPLETED,
                'description' => 'Withdrawal of 15.00 TON to wallet EQA...abc123',
                'created_at' => now()->subDays(3),
            ],
            // Pending withdrawal
            [
                'type' => TransactionType::WITHDRAWAL,
                'value' => 8.25,
                'status' => TransactionStatus::PENDING,
                'description' => 'Withdrawal of 8.25 TON to wallet EQB...def456',
                'created_at' => now()->subHours(6),
            ],
            // Failed boost payment
            [
                'type' => TransactionType::BOOST_PAYMENT,
                'value' => 1.75,
                'status' => TransactionStatus::FAILED,
                'description' => 'Purchase of Efficiency Boost for 1.75 TON',
                'created_at' => now()->subDays(5),
            ],
            // Older successful boost payment
            [
                'type' => TransactionType::BOOST_PAYMENT,
                'value' => 3.00,
                'status' => TransactionStatus::COMPLETED,
                'description' => 'Purchase of Power Boost for 3.00 TON',
                'created_at' => now()->subWeek(),
            ],
            // Multiple referral bonuses
            [
                'type' => TransactionType::REFERRAL_BONUS,
                'value' => 0.50,
                'status' => TransactionStatus::COMPLETED,
                'description' => 'Referral bonus for referring Alex Johnson',
                'created_at' => now()->subDays(10),
            ],
            [
                'type' => TransactionType::REFERRAL_BONUS,
                'value' => 0.35,
                'status' => TransactionStatus::COMPLETED,
                'description' => 'Referral bonus for referring Maria Rodriguez',
                'created_at' => now()->subDays(12),
            ],
            // Cancelled withdrawal
            [
                'type' => TransactionType::WITHDRAWAL,
                'value' => 5.50,
                'status' => TransactionStatus::CANCELLED,
                'description' => 'Withdrawal of 5.50 TON to wallet EQC...ghi789',
                'created_at' => now()->subDays(15),
            ],
            // Older boost payments
            [
                'type' => TransactionType::BOOST_PAYMENT,
                'value' => 1.25,
                'status' => TransactionStatus::COMPLETED,
                'description' => 'Purchase of Speed Boost for 1.25 TON',
                'created_at' => now()->subDays(18),
            ],
            [
                'type' => TransactionType::BOOST_PAYMENT,
                'value' => 4.50,
                'status' => TransactionStatus::COMPLETED,
                'description' => 'Purchase of Premium Power Boost for 4.50 TON',
                'created_at' => now()->subDays(20),
            ],
            // Large withdrawal from a month ago
            [
                'type' => TransactionType::WITHDRAWAL,
                'value' => 25.00,
                'status' => TransactionStatus::COMPLETED,
                'description' => 'Withdrawal of 25.00 TON to wallet EQD...jkl012',
                'created_at' => now()->subDays(25),
            ],
        ];

        foreach ($transactions as $transactionData) {
            Transaction::create([
                'user_id' => $user->id,
                'staff_id' => null,
                'type' => $transactionData['type'],
                'value' => $transactionData['value'],
                'status' => $transactionData['status'],
                'hash' => Transaction::generateHash(),
                'description' => $transactionData['description'],
                'created_at' => $transactionData['created_at'],
                'updated_at' => $transactionData['created_at'],
            ]);
        }

        $this->command->info("Created " . count($transactions) . " transactions for johndoe");
    }
}
