<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Seed application settings
        $this->call([
            SettingSeeder::class,
            StaffSeeder::class,
            UserSeeder::class,
            TransactionSeeder::class,
        ]);

        // User::factory(10)->create();

        // Test user with email (if needed for testing)
        // User::factory()->create([
        //     'name' => 'Test User',
        //     'email' => '<EMAIL>',
        // ]);
    }
}
