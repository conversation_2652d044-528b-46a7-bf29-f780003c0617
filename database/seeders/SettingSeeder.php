<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class SettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = $this->getDefaultSettings();

        foreach ($settings as $setting) {
            $this->createSettingIfNotExists($setting);
        }

        $this->command->info('Settings seeded successfully!');
    }

    /**
     * Create setting only if it doesn't exist
     */
    private function createSettingIfNotExists(array $setting): void
    {
        $existingSetting = Setting::where('key', $setting['key'])->first();

        if (! $existingSetting) {
            Setting::create($setting);
            $this->command->info("Created setting: {$setting['key']}");
        } else {
            $existingSetting->update($setting);
            $this->command->info("Updated setting: {$setting['key']}");
        }
    }

    /**
     * Get default settings to be seeded
     */
    private function getDefaultSettings(): array
    {
        return [
            [
                'key' => 'convert_rate',
                'value' => 2.06,
                'type' => 'number',
                'description' => 'Ton mining rate',
                'is_public' => true,
            ],
            [
                'key' => 'base_speed',
                'value' => 2.4,
                'type' => 'number',
                'description' => 'Base Mining Speed',
                'is_public' => false,
            ],
            [
                'key' => 'minimum_claim_ton_amount',
                'value' => 0.1,
                'type' => 'number',
                'description' => 'Minimum claim ton amount',
                'is_public' => true,
            ],
            [
                'key' => 'minimum_withdrawal_ton_amount',
                'value' => 0.35,
                'type' => 'number',
                'description' => 'Minimum withdrawal ton amount',
                'is_public' => true,
            ],
            [
                'key' => 'minimum_boot_amount',
                'value' => 3,
                'type' => 'number',
                'description' => 'Minimum boot amount',
                'is_public' => true,
            ],
            [
                'key' => 'minimum_withdrawal_shiba_amount',
                'value' => 700000,
                'type' => 'number',
                'description' => 'Minimum withdrawal shiba amount',
                'is_public' => true,
            ],
            [
                'key' => 'ton_withdrawal_fee_percent',
                'value' => 0.05,
                'type' => 'number',
                'description' => 'Ton withdrawal fee percent',
                'is_public' => true,
            ],
            [
                'key' => 'ton_wallet_address',
                'value' => 'UQAbbwx_c-O7Eg6DKIvMAKspV__zzQF0VKqLN0QqL1jnz9_j',
                'type' => 'string',
                'description' => 'Ton wallet receive address',
                'is_public' => true,
            ],
            [
                'key' => 'telegram_bot_token',
                'value' => '7053594957:AAHQQoNZYEZrvTtW8Uz20oYemmdta0Rh3Vw',
                'description' => 'Telegram bot token',
                'is_public' => false,
            ],
            [
                'key' => 'star_conversion_rate',
                'value' => 0.013,
                'type' => 'number',
                'description' => 'Star to USDT conversion rate',
                'is_public' => true,
            ]
        ];
    }
}
