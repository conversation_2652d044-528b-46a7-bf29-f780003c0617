# Transaction Seeder

This directory contains the `TransactionSeeder` class that generates sample transaction data for a user with the username "johndo<PERSON>".

## Overview

The `TransactionSeeder` creates realistic transaction data for testing and development purposes. It:

1. **Finds or creates a user** with username "johndo<PERSON>"
2. **Generates 12 realistic transactions** with various types, amounts, and statuses
3. **Includes proper timestamps** spanning from recent to historical data
4. **Follows Laravel seeder best practices**

## Transaction Types Generated

The seeder creates transactions of the following types:

- **Boost Payments** (`boost_payment`): Purchases of various boosts (Speed, Efficiency, Power)
- **Referral Bonuses** (`referral_bonus`): Bonuses earned from successful referrals
- **Withdrawals** (`withdrawal`): TON withdrawals to various wallet addresses

## Transaction Statuses

Transactions are created with realistic status distributions:

- **Completed**: Most transactions (successful operations)
- **Pending**: Some recent transactions still processing
- **Failed**: Occasional failed transactions
- **Cancelled**: Some cancelled operations

## Usage

### Run as part of full database seeding:

```bash
# Using Docker (recommended)
docker-compose exec backend php artisan migrate:fresh --seed

# Or directly (if not using Docker)
php artisan migrate:fresh --seed
```

### Run only the TransactionSeeder:

```bash
# Using Docker (recommended)
docker-compose exec backend php artisan db:seed --class=TransactionSeeder

# Or directly (if not using Docker)
php artisan db:seed --class=TransactionSeeder
```

### Run using the custom command:

```bash
# Using Docker (recommended)
docker-compose exec backend php artisan seed:transactions

# Or directly (if not using Docker)
php artisan seed:transactions
```

## User Details

The seeder creates/uses a user with the following details:

- **Name**: John Doe
- **Username**: johndoe
- **Telegram ID**: 999888777
- **Country**: US
- **Language**: en
- **Balance TON**: 25.75
- **Balance Token**: 3500.00
- **Speed**: 4.25
- **Pending Token**: 225.50

## Sample Transaction Data

The seeder generates 12 transactions including:

1. Recent boost payment (2 hours ago) - COMPLETED
2. Referral bonus (1 day ago) - COMPLETED
3. Withdrawal (3 days ago) - COMPLETED
4. Pending withdrawal (6 hours ago) - PENDING
5. Failed boost payment (5 days ago) - FAILED
6. Successful boost payment (1 week ago) - COMPLETED
7. Referral bonus (10 days ago) - COMPLETED
8. Another referral bonus (12 days ago) - COMPLETED
9. Cancelled withdrawal (15 days ago) - CANCELLED
10. Older boost payment (18 days ago) - COMPLETED
11. Premium boost payment (20 days ago) - COMPLETED
12. Large withdrawal (25 days ago) - COMPLETED

## Testing

The seeder includes comprehensive tests in `tests/Feature/TransactionSeederTest.php`:

```bash
# Run the tests using Docker
docker-compose exec backend php artisan test tests/Feature/TransactionSeederTest.php

# Or directly (if not using Docker)
php artisan test tests/Feature/TransactionSeederTest.php
```

## Features

- **Idempotent**: Can be run multiple times safely
- **Realistic Data**: Uses appropriate amounts and descriptions
- **Unique Hashes**: All transactions have unique hash identifiers
- **Proper Relationships**: Correctly links transactions to the user
- **Timestamp Variety**: Creates transactions across different time periods
- **Status Diversity**: Includes all possible transaction statuses

## Integration

The `TransactionSeeder` is automatically included in the `DatabaseSeeder` class and will run as part of the standard seeding process.
