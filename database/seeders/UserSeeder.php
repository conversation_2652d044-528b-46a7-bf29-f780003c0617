<?php

namespace Database\Seeders;

use App\Enums\TransactionStatus;
use App\Enums\TransactionType;
use App\Models\Transaction;
use App\Models\User;
use Illuminate\Database\Seeder;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create realistic users with different profiles
        $users = [
            [
                'name' => '<PERSON>',
                'username' => 'alex<PERSON><PERSON><PERSON>',
                'tele_id' => '123456789',
                'country' => 'US',
                'language_code' => 'en',
                'balance_ton' => 15.75,
                'balance_token' => 2500.00,
                'speed' => 3.25,
                'pending_token' => 125.50,
            ],
            [
                'name' => '<PERSON>',
                'username' => 'mariarodriguez',
                'tele_id' => '987654321',
                'country' => 'ES',
                'language_code' => 'es',
                'balance_ton' => 8.42,
                'balance_token' => 1800.00,
                'speed' => 2.75,
                'pending_token' => 87.25,
            ],
            [
                'name' => '<PERSON>',
                'username' => 'weichen',
                'tele_id' => '555666777',
                'country' => 'CN',
                'language_code' => 'zh',
                'balance_ton' => 22.18,
                'balance_token' => 3200.00,
                'speed' => 4.50,
                'pending_token' => 198.75,
            ],
            [
                'name' => 'Sarah Williams',
                'username' => 'sarahwilliams',
                'tele_id' => '111222333',
                'country' => 'GB',
                'language_code' => 'en',
                'balance_ton' => 5.93,
                'balance_token' => 950.00,
                'speed' => 1.85,
                'pending_token' => 42.80,
            ],
            [
                'name' => 'Dmitri Petrov',
                'username' => 'dmitripetrov',
                'tele_id' => '444555666',
                'country' => 'RU',
                'language_code' => 'ru',
                'balance_ton' => 31.67,
                'balance_token' => 4500.00,
                'speed' => 5.25,
                'pending_token' => 267.40,
            ],
            [
                'name' => 'Emma Thompson',
                'username' => 'emmathompson',
                'tele_id' => '777888999',
                'country' => 'CA',
                'language_code' => 'en',
                'balance_ton' => 12.34,
                'balance_token' => 2100.00,
                'speed' => 2.95,
                'pending_token' => 156.20,
            ],
            [
                'name' => 'Hans Mueller',
                'username' => 'hansmueller',
                'tele_id' => '333444555',
                'country' => 'DE',
                'language_code' => 'de',
                'balance_ton' => 18.91,
                'balance_token' => 2800.00,
                'speed' => 3.75,
                'pending_token' => 189.60,
            ],
            [
                'name' => 'Yuki Tanaka',
                'username' => 'yukitanaka',
                'tele_id' => '666777888',
                'country' => 'JP',
                'language_code' => 'ja',
                'balance_ton' => 7.58,
                'balance_token' => 1400.00,
                'speed' => 2.25,
                'pending_token' => 78.90,
            ],
            [
                'name' => 'Lucas Silva',
                'username' => 'lucassilva',
                'tele_id' => '999000111',
                'country' => 'BR',
                'language_code' => 'pt',
                'balance_ton' => 14.26,
                'balance_token' => 2300.00,
                'speed' => 3.15,
                'pending_token' => 145.75,
            ],
            [
                'name' => 'Priya Sharma',
                'username' => 'priyasharma',
                'tele_id' => '222333444',
                'country' => 'IN',
                'language_code' => 'en',
                'balance_ton' => 9.82,
                'balance_token' => 1600.00,
                'speed' => 2.60,
                'pending_token' => 98.30,
            ],
        ];

        foreach ($users as $userData) {
            $user = User::create([
                'name' => $userData['name'],
                'username' => $userData['username'],
                'tele_id' => $userData['tele_id'],
                'country' => $userData['country'],
                'language_code' => $userData['language_code'],
                'balance_ton' => $userData['balance_ton'],
                'balance_token' => $userData['balance_token'],
                'speed' => $userData['speed'],
                'pending_token' => $userData['pending_token'],
                'last_active' => now()->subMinutes(rand(1, 1440)), // Random last activity within 24 hours
                'last_checkpoint' => now()->subMinutes(rand(1, 120)), // Random checkpoint within 2 hours
                'avatar_url' => 'https://api.dicebear.com/7.x/avataaars/svg?seed='.$userData['username'],
            ]);

            // Generate referral code for each user
            $user->generateReferralCode();

            // Create transactions for each user
            $this->createTransactionsForUser($user);
        }

        // Create referral relationships between some users
        $this->createReferralRelationships();
    }

    /**
     * Create realistic transactions for a user
     */
    private function createTransactionsForUser(User $user): void
    {
        // Generate 3-8 transactions per user
        $transactionCount = rand(3, 8);

        for ($i = 0; $i < $transactionCount; $i++) {
            $transactionType = $this->getRandomTransactionType();
            $amount = $this->getRandomAmount($transactionType);
            $status = $this->getRandomStatus();
            $createdAt = now()->subDays(rand(1, 30))->subHours(rand(1, 24));

            Transaction::create([
                'user_id' => $user->id,
                'staff_id' => null,
                'type' => $transactionType,
                'value' => $amount,
                'status' => $status,
                'hash' => Transaction::generateHash(),
                'description' => $this->getTransactionDetails($transactionType, $amount),
                'created_at' => $createdAt,
                'updated_at' => $createdAt,
            ]);
        }
    }

    /**
     * Get random transaction type
     */
    private function getRandomTransactionType(): string
    {
        $types = [
            TransactionType::BOOST_PAYMENT,
            TransactionType::REFERRAL_BONUS,
            TransactionType::WITHDRAWAL,
        ];

        return $types[array_rand($types)];
    }

    /**
     * Get random amount based on transaction type
     */
    private function getRandomAmount(string $type): float
    {
        switch ($type) {
            case TransactionType::BOOST_PAYMENT:
                return round(mt_rand(50, 500) / 100, 2); // 0.50 to 5.00 TON
            case TransactionType::REFERRAL_BONUS:
                return round(mt_rand(10, 100) / 100, 2); // 0.10 to 1.00 TON
            case TransactionType::WITHDRAWAL:
                return round(mt_rand(100, 2000) / 100, 2); // 1.00 to 20.00 TON
            default:
                return 1.00;
        }
    }

    /**
     * Get random transaction status
     */
    private function getRandomStatus(): string
    {
        $statuses = [
            TransactionStatus::COMPLETED => 70, // 70% completed
            TransactionStatus::PENDING => 20,   // 20% pending
            TransactionStatus::FAILED => 7,     // 7% failed
            TransactionStatus::CANCELLED => 3,  // 3% cancelled
        ];

        $rand = rand(1, 100);
        $cumulative = 0;

        foreach ($statuses as $status => $percentage) {
            $cumulative += $percentage;
            if ($rand <= $cumulative) {
                return $status;
            }
        }

        return TransactionStatus::COMPLETED;
    }

    /**
     * Get transaction details based on type
     */
    private function getTransactionDetails(string $type, float $amount): string
    {
        switch ($type) {
            case TransactionType::BOOST_PAYMENT:
                $boostTypes = ['Speed Boost', 'Efficiency Boost', 'Power Boost'];
                $boostType = $boostTypes[array_rand($boostTypes)];

                return "Purchase of {$boostType} for {$amount} TON";

            case TransactionType::REFERRAL_BONUS:
                return 'Referral bonus earned from successful referral';

            case TransactionType::WITHDRAWAL:
                $walletAddresses = [
                    'EQA...abc123',
                    'EQB...def456',
                    'EQC...ghi789',
                    'EQD...jkl012',
                    'EQE...mno345',
                ];
                $wallet = $walletAddresses[array_rand($walletAddresses)];

                return "Withdrawal of {$amount} TON to wallet {$wallet}";

            default:
                return "Transaction of {$amount} TON";
        }
    }

    /**
     * Create referral relationships between users
     */
    private function createReferralRelationships(): void
    {
        $users = User::all();
        $usersArray = $users->toArray();

        // Create 5-8 referral relationships
        for ($i = 0; $i < rand(5, 8); $i++) {
            $referrer = $users->random();
            $referred = $users->where('id', '!=', $referrer->id)->random();

            // Check if referred user can be referred by this referrer
            if ($referred->canBeReferredBy($referrer)) {
                $referred->createReferralRelationship($referrer);

                // Create a referral bonus transaction for the referrer
                Transaction::create([
                    'user_id' => $referrer->id,
                    'staff_id' => null,
                    'type' => TransactionType::REFERRAL_BONUS,
                    'value' => round(mt_rand(10, 50) / 100, 2), // 0.10 to 0.50 TON
                    'status' => TransactionStatus::COMPLETED,
                    'hash' => Transaction::generateHash(),
                    'description' => "Referral bonus for referring {$referred->name}",
                    'created_at' => now()->subDays(rand(1, 15)),
                    'updated_at' => now()->subDays(rand(1, 15)),
                ]);
            }
        }
    }
}
