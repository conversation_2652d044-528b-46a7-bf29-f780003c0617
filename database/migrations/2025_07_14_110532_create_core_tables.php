<?php

use App\Enums\TransactionStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create users table
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('country')->nullable();
            $table->string('tele_id')->unique();
            $table->string('name');
            $table->string('username')->nullable();
            $table->string('language_code')->nullable();
            $table->string('avatar_url')->nullable();
            $table->timestamp('last_active')->nullable();
            $table->decimal('balance_ton', 20, 8)->default(0);
            $table->decimal('balance_token', 20, 8)->default(0);
            $table->string('referral_code', 8)->nullable()->unique();
            $table->decimal('speed', 20, 8)->default(0);
            $table->decimal('pending_token', 20, 8)->default(0);
            $table->timestamp('last_checkpoint')->nullable();
            $table->timestamp('boost_expired_at')->nullable();
            $table->timestamps();
        });

        // Create staff table
        Schema::create('staff', function (Blueprint $table) {
            $table->id();
            $table->string('username')->unique();
            $table->string('password');
            $table->timestamp('last_active')->nullable();
            $table->timestamps();
        });

        // Create personal access tokens table (Laravel Sanctum)
        Schema::create('personal_access_tokens', function (Blueprint $table) {
            $table->id();
            $table->morphs('tokenable');
            $table->string('name');
            $table->string('token', 64)->unique();
            $table->text('abilities')->nullable();
            $table->timestamp('last_used_at')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->timestamps();
        });

        // Create transactions table
        Schema::create('transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('staff_id')->nullable()->constrained()->onDelete('set null');
            $table->string('type');
            $table->decimal('value', 20, 8)->index();
            $table->string('status')->default(TransactionStatus::PENDING);
            $table->string('hash')->nullable()->index();
            $table->text('description')->nullable();
            $table->timestamps();
        });

        // Create refs table (referral system)
        Schema::create('refs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade'); // referrer
            $table->foreignId('ref_id')->constrained('users')->onDelete('cascade'); // referred user
            $table->decimal('commission', 20, 8)->default(0);
            $table->timestamps();
        });

        // Create blocked table
        Schema::create('blocked', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('staff_id')->constrained()->onDelete('cascade');
            $table->text('reason')->nullable();
            $table->timestamps();
        });

        // Create withdrawals table
        Schema::create('withdrawals', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('staff_id')->nullable()->constrained()->onDelete('set null');
            $table->decimal('amount', 20, 8);
            $table->string('currency', 10)->default('TON');
            $table->string('address');
            $table->string('status')->default('pending'); // pending, approved, rejected, completed
            $table->text('notes')->nullable();
            $table->timestamps();
        });

        // Create settings table
        Schema::create('settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique();
            $table->text('value')->nullable();
            $table->string('type')->default('string'); // string, number, boolean, json
            $table->text('description')->nullable();
            $table->boolean('is_public')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('settings');
        Schema::dropIfExists('withdrawals');
        Schema::dropIfExists('blocked');
        Schema::dropIfExists('refs');
        Schema::dropIfExists('transactions');
        Schema::dropIfExists('wallets');
        Schema::dropIfExists('personal_access_tokens');
        Schema::dropIfExists('staff');
        Schema::dropIfExists('users');
    }
};
