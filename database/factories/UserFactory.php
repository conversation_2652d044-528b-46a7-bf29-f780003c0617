<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
class UserFactory extends Factory
{
    /**
     * The current password being used by the factory.
     */
    protected static ?string $password;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'tele_id' => fake()->unique()->numerify('##########'),
            'name' => fake()->name(),
            'country' => fake()->countryCode(),
            'language_code' => fake()->randomElement(['en', 'fr', 'es', 'de', 'ru']),
            'avatar_url' => fake()->optional()->imageUrl(100, 100, 'people'),
            'last_active' => now(),
            'balance_ton' => 0,
            'balance_token' => 0,
        ];
    }
}
