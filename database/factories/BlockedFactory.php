<?php

namespace Database\Factories;

use App\Models\Staff;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Blocked>
 */
class BlockedFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'staff_id' => Staff::factory(),
            'reason' => fake()->sentence(),
        ];
    }

    /**
     * Create a blocked record for specific user and staff.
     */
    public function forUserAndStaff(User $user, Staff $staff): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => $user->id,
            'staff_id' => $staff->id,
        ]);
    }

    /**
     * Create a blocked record with specific reason.
     */
    public function withReason(string $reason): static
    {
        return $this->state(fn (array $attributes) => [
            'reason' => $reason,
        ]);
    }
}
