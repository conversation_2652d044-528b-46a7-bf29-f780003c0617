<?php

declare(strict_types=1);

namespace App\Enums;

use <PERSON>Samp<PERSON>\Enum\Enum;

/**
 * @method static static TON_PAYMENT()
 * @method static static BOOST_PAYMENT()
 * @method static static TELEGRAM_STARS_PAYMENT()
 * @method static static REFERRAL_BONUS()
 * @method static static DEPOSIT()
 * @method static static WITHDRAWAL()
 */
final class TransactionType extends Enum
{
    const BOOST_PAYMENT = 'boost_payment';

    const TELEGRAM_STARS_PAYMENT = 'telegram_stars_payment';

    const REFERRAL_BONUS = 'referral_bonus';

    const WITHDRAWAL = 'withdrawal';

    const TON_DEPOSIT = 'ton_deposit';

    public static function getDepositTypes(): array
    {
        return [
            self::BOOST_PAYMENT,
            self::TELEGRAM_STARS_PAYMENT,
            self::TON_DEPOSIT,
        ];
    }

    public static function getWithdrawalTypes(): array
    {
        return [
            self::WITHDRAWAL,
        ];
    }

    public static function getBonusTypes(): array
    {
        return [
            self::REFERRAL_BONUS,
        ];
    }

    public static function getTypesForGroup(string $group): array
    {
        switch ($group) {
            case 'deposit':
                return self::getDepositTypes();
            case 'withdrawal':
                return self::getWithdrawalTypes();
            case 'bonus':
                return self::getBonusTypes();
            default:
                return [];
        }
    }

    public static function getGroupForType(string $type): string
    {
        if (in_array($type, self::getDepositTypes())) {
            return 'deposit';
        }

        if (in_array($type, self::getWithdrawalTypes())) {
            return 'withdrawal';
        }

        if (in_array($type, self::getBonusTypes())) {
            return 'bonus';
        }

        return 'deposit';
    }
}
