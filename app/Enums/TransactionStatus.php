<?php

namespace App\Enums;

use BenSampo\Enum\Enum;

/**
 * @method static static PENDING()
 * @method static static COMPLETED()
 * @method static static FAILED()
 * @method static static CANCELLED()
 */
final class TransactionStatus extends Enum
{
    const PENDING = 'pending';

    const COMPLETED = 'completed';

    const FAILED = 'failed';

    const CANCELLED = 'cancelled';

    const PROCESSING = 'processing';
}
