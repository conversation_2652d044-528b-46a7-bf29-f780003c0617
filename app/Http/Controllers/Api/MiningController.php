<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\BoostService;
use App\Services\SettingsService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class MiningController extends Controller
{
    protected $boostService;

    public function __construct(BoostService $boostService)
    {
        $this->boostService = $boostService;
    }

    public function claimed(Request $request)
    {
        $user = $request->user();

        $settingsService = new SettingsService();
        $minimumClaimAmount = $settingsService->getSettingsByKeys(['minimum_claim_ton_amount'])['minimum_claim_ton_amount'];

        $amountToClaim = $this->boostService->calculatePendingTokens($user) + $user->pending_token;

        if ($minimumClaimAmount > $amountToClaim) {
            return response()->json([
                'success' => false,
                'message' => 'You need to have at least ' . $minimumClaimAmount . ' TON to claim',
            ], 400);
        }

        try {

            $user->update([
                'balance_ton' => ($user->balance_ton ?? 0) + $amountToClaim,
                'pending_token' => 0,
                'last_checkpoint' => Carbon::now(),
            ]);

            Log::info('User claimed mining tokens', [
                'user_id' => $user->id,
                'claimed_amount' => $amountToClaim,
                'new_balance' => $user->balance_ton,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Mining tokens claimed successfully',
                'data' => [
                    'claimed_amount' => $amountToClaim,
                    'new_balance' => $user->balance_ton,
                    'pending_token' => 0,
                    'claimed_at' => Carbon::now(),
                ],
            ], 200);

        } catch (\Exception $e) {
            Log::error('Failed to claim mining tokens', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to claim mining tokens',
            ], 500);
        }
    }
}
