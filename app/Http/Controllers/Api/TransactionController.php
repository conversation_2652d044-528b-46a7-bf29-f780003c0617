<?php

namespace App\Http\Controllers\Api;

use App\Enums\TransactionType;
use App\Http\Controllers\Controller;
use App\Models\Transaction;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class TransactionController extends Controller
{
    /**
     * Get transactions for admin (with optional user_id and group filtering)
     */
    public function adminList(Request $request)
    {
        $request->validate([
            'user_id' => 'sometimes|integer|exists:users,id',
            'group' => ['sometimes', 'string', Rule::in([
                'deposit',
                'withdrawal',
                'bonus',
            ])],
            'per_page' => 'sometimes|integer|min:1|max:100',
            'page' => 'sometimes|integer|min:1',
        ]);

        $query = Transaction::with(['user:id,first_name,last_name,username,tele_id']);

        // Filter by user_id if provided
        if ($request->has('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        // Filter by group type if provided
        if ($request->has('group')) {
            $groupTypes = TransactionType::getTypesForGroup($request->group);
            $query->whereIn('type', $groupTypes);
        }

        // Order by most recent first
        $query->orderBy('created_at', 'desc');

        $perPage = $request->get('per_page', 15);
        $transactions = $query->paginate($perPage);

        // Add group attribute to each transaction
        $transactions->getCollection()->transform(function ($transaction) {
            $transaction->group = TransactionType::getGroupForType($transaction->type);

            return $transaction;
        });

        return response()->json([
            'success' => true,
            'message' => 'Transactions retrieved successfully',
            'data' => $transactions,
        ], 200);
    }

    /**
     * Get transactions for authenticated user (with optional group filtering)
     */
    public function userList(Request $request)
    {
        $request->validate([
            'type' => ['sometimes', 'string', Rule::in([
                'deposit',
                'withdrawal',
                'bonus',
            ])],
            'per_page' => 'sometimes|integer|min:1|max:50',
            'page' => 'sometimes|integer|min:1',
        ]);

        $query = Transaction::where('user_id', $request->user()->id);

        // Filter by type if provided
        if ($request->has('type')) {
            $groupTypes = TransactionType::getTypesForGroup($request->type);
            $query->whereIn('type', $groupTypes);
        }

        // Order by most recent first
        $query->orderBy('created_at', 'desc');

        $perPage = $request->get('per_page', 15);
        $transactions = $query->paginate($perPage);

        // Add group attribute to each transaction
        $transactions->getCollection()->transform(function ($transaction) {
            $transaction->group = TransactionType::getGroupForType($transaction->type);

            return $transaction;
        });

        return response()->json([
            'success' => true,
            'message' => 'Your transactions retrieved successfully',
            'data' => $transactions,
        ], 200);
    }
}
