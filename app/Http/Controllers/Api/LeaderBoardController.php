<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;

class LeaderBoardController extends Controller
{
    public function topSpeed(Request $request)
    {
        $fakeUsers = $this->generateFakeSpeedUsers();

        $realUsers = User::whereNotNull('tele_id')
            ->where('speed', '>', 0)
            ->orderBy('speed', 'desc')
            ->take(12)
            ->get()
            ->map(function ($user, $index) use ($request) {
                return [
                    'rank' => $index + 9,
                    'name' => $user->name ?? 'Anonymous',
                    'username' => $user->username ? '@'.$user->username : null,
                    'speed' => (float) $user->speed,
                    'avatar_url' => $user->avatar_url,
                    'is_current_user' => $request->user() && $request->user()->id === $user->id,
                ];
            });

        $leaderboard = array_merge($fakeUsers, $realUsers->toArray());

        $leaderboard = array_slice($leaderboard, 0, 20);

        foreach ($leaderboard as $index => &$user) {
            $user['rank'] = $index + 1;
        }

        return response()->json([
            'success' => true,
            'data' => [
                'type' => 'speed',
                'leaderboard' => $leaderboard,
                'total_users' => count($leaderboard),
                'updated_at' => now(),
            ],
        ]);
    }

    public function topBalance(Request $request)
    {
        $fakeUsers = $this->generateFakeBalanceUsers();

        $realUsers = User::whereNotNull('tele_id')
            ->where('balance_ton', '>', 0)
            ->orderBy('balance_ton', 'desc')
            ->take(12)
            ->get()
            ->map(function ($user, $index) use ($request) {
                return [
                    'rank' => $index + 9,
                    'name' => $user->name ?? 'Anonymous',
                    'username' => $user->username ? '@'.$user->username : null,
                    'balance' => (float) $user->balance_ton,
                    'avatar_url' => $user->avatar_url,
                    'is_current_user' => $request->user() && $request->user()->id === $user->id,
                ];
            });

        $leaderboard = array_merge($fakeUsers, $realUsers->toArray());

        $leaderboard = array_slice($leaderboard, 0, 20);

        foreach ($leaderboard as $index => &$user) {
            $user['rank'] = $index + 1;
        }

        return response()->json([
            'success' => true,
            'data' => [
                'type' => 'balance',
                'leaderboard' => $leaderboard,
                'total_users' => count($leaderboard),
                'updated_at' => now(),
            ],
        ]);
    }

    public function userRank(Request $request)
    {
        $user = $request->user();

        if (! $user) {
            return response()->json([
                'success' => false,
                'message' => 'User not authenticated',
            ], 401);
        }

        $speedRank = User::where('speed', '>', $user->speed)->count() + 9;
        $balanceRank = User::where('balance_ton', '>', $user->balance_ton)->count() + 9;

        return response()->json([
            'success' => true,
            'data' => [
                'user_id' => $user->id,
                'name' => $user->name,
                'username' => $user->username,
                'speed' => (float) $user->speed,
                'balance_ton' => (float) $user->balance_ton,
                'ranks' => [
                    'speed' => $speedRank,
                    'balance' => $balanceRank,
                ],
            ],
        ]);
    }

    private function generateFakeSpeedUsers(): array
    {
        $fakeUsers = [
            [
                'rank' => 1,
                'name' => 'CryptoMiner2024',
                'username' => '@cryptominer2024',
                'speed' => 18.75,
                'avatar_url' => 'https://api.dicebear.com/7.x/avataaars/svg?seed=CryptoMiner2024',
                'is_current_user' => false,
            ],
            [
                'rank' => 2,
                'name' => 'HashMaster',
                'username' => '@hashmaster',
                'speed' => 17.42,
                'avatar_url' => 'https://api.dicebear.com/7.x/avataaars/svg?seed=HashMaster',
                'is_current_user' => false,
            ],
            [
                'rank' => 3,
                'name' => 'TON_Wizard',
                'username' => '@ton_wizard',
                'speed' => 16.89,
                'avatar_url' => 'https://api.dicebear.com/7.x/avataaars/svg?seed=TON_Wizard',
                'is_current_user' => false,
            ],
            [
                'rank' => 4,
                'name' => 'DigitalGold',
                'username' => '@digitalgold',
                'speed' => 15.67,
                'avatar_url' => 'https://api.dicebear.com/7.x/avataaars/svg?seed=DigitalGold',
                'is_current_user' => false,
            ],
            [
                'rank' => 5,
                'name' => 'BlockchainKing',
                'username' => '@blockchainKing',
                'speed' => 14.93,
                'avatar_url' => 'https://api.dicebear.com/7.x/avataaars/svg?seed=BlockchainKing',
                'is_current_user' => false,
            ],
            [
                'rank' => 6,
                'name' => 'MiningLegend',
                'username' => '@mininglegend',
                'speed' => 13.28,
                'avatar_url' => 'https://api.dicebear.com/7.x/avataaars/svg?seed=MiningLegend',
                'is_current_user' => false,
            ],
            [
                'rank' => 7,
                'name' => 'TON_Hunter',
                'username' => '@ton_hunter',
                'speed' => 12.54,
                'avatar_url' => 'https://api.dicebear.com/7.x/avataaars/svg?seed=TON_Hunter',
                'is_current_user' => false,
            ],
            [
                'rank' => 8,
                'name' => 'CryptoNinja',
                'username' => '@cryptoninja',
                'speed' => 11.76,
                'avatar_url' => 'https://api.dicebear.com/7.x/avataaars/svg?seed=CryptoNinja',
                'is_current_user' => false,
            ],
        ];

        return $fakeUsers;
    }

    private function generateFakeBalanceUsers(): array
    {
        $fakeUsers = [
            [
                'rank' => 1,
                'name' => 'TON_Whale',
                'username' => '@ton_whale',
                'balance' => 2847.52,
                'avatar_url' => 'https://api.dicebear.com/7.x/avataaars/svg?seed=TON_Whale',
                'is_current_user' => false,
            ],
            [
                'rank' => 2,
                'name' => 'DiamondHands',
                'username' => '@diamondhands',
                'balance' => 2156.89,
                'avatar_url' => 'https://api.dicebear.com/7.x/avataaars/svg?seed=DiamondHands',
                'is_current_user' => false,
            ],
            [
                'rank' => 3,
                'name' => 'CryptoTycoon',
                'username' => '@cryptotycoon',
                'balance' => 1876.34,
                'avatar_url' => 'https://api.dicebear.com/7.x/avataaars/svg?seed=CryptoTycoon',
                'is_current_user' => false,
            ],
            [
                'rank' => 4,
                'name' => 'BlockchainBaron',
                'username' => '@blockchainbaron',
                'balance' => 1654.27,
                'avatar_url' => 'https://api.dicebear.com/7.x/avataaars/svg?seed=BlockchainBaron',
                'is_current_user' => false,
            ],
            [
                'rank' => 5,
                'name' => 'TON_Collector',
                'username' => '@ton_collector',
                'balance' => 1432.98,
                'avatar_url' => 'https://api.dicebear.com/7.x/avataaars/svg?seed=TON_Collector',
                'is_current_user' => false,
            ],
            [
                'rank' => 6,
                'name' => 'CryptoSavant',
                'username' => '@cryptosavant',
                'balance' => 1298.76,
                'avatar_url' => 'https://api.dicebear.com/7.x/avataaars/svg?seed=CryptoSavant',
                'is_current_user' => false,
            ],
            [
                'rank' => 7,
                'name' => 'DigitalMogul',
                'username' => '@digitalmogul',
                'balance' => 1087.43,
                'avatar_url' => 'https://api.dicebear.com/7.x/avataaars/svg?seed=DigitalMogul',
                'is_current_user' => false,
            ],
            [
                'rank' => 8,
                'name' => 'TON_Master',
                'username' => '@ton_master',
                'balance' => 956.82,
                'avatar_url' => 'https://api.dicebear.com/7.x/avataaars/svg?seed=TON_Master',
                'is_current_user' => false,
            ],
        ];

        return $fakeUsers;
    }
}
