<?php

namespace App\Http\Controllers\Api;

use App\Enums\TransactionStatus;
use App\Enums\TransactionType;
use App\Helpers\TonHelper;
use App\Http\Controllers\Controller;
use App\Models\Transaction;
use App\Services\BoostService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BoosterController extends Controller
{
    protected $tonHelper;

    protected $boostService;

    public function __construct(TonHelper $tonHelper, BoostService $boostService)
    {
        $this->tonHelper = $tonHelper;
        $this->boostService = $boostService;
    }

    public function boost(Request $request)
    {
        $request->validate([
            'ton_amount' => 'required|numeric|min:3',
        ]);

        $user_id = $request->user()->id;
        $tonAmount = $request->ton_amount;
        $nanoAmountTon = $tonAmount * 1000000000; // Convert TON to nanoTON

        // Validate the transaction on TON blockchain
        $isSuccess = $this->validateBoostTransaction($user_id, $nanoAmountTon);

        if ($isSuccess) {
            try {
                DB::beginTransaction();

                // Apply boost to user
                $boostResult = $this->boostService->applyBoost($request->user(), $tonAmount);

                if (! $boostResult) {
                    throw new \Exception('Failed to apply boost to user');
                }

                // Create transaction record
                $transaction = Transaction::create([
                    'type' => TransactionType::BOOST_PAYMENT,
                    'value' => $nanoAmountTon,
                    'description' => 'Boost Payment - mining_lite_'.$user_id.' - Boost Applied',
                    'user_id' => $user_id,
                    'status' => TransactionStatus::COMPLETED,
                    'hash' => Transaction::generateHash(),
                ]);

                DB::commit();

                return response()->json([
                    'success' => true,
                    'message' => 'Boost payment completed successfully',
                    'data' => [
                        'transaction_hash' => $transaction->hash,
                        'ton_amount' => $tonAmount,
                        'boost_expired_at' => $request->user()->fresh()->boost_expired_at,
                        'new_speed' => $request->user()->fresh()->speed,
                    ],
                ], 200);

            } catch (\Exception $e) {
                DB::rollBack();

                Log::error('Failed to apply boost after TON payment confirmation.', [
                    'user_id' => $user_id,
                    'ton_amount' => $tonAmount,
                    'error' => $e->getMessage(),
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Transaction confirmed on blockchain, but failed to apply boost.',
                ], 500);
            }
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Transaction not found on blockchain or failed validation',
                'data' => [
                    'expected_message' => 'mining_lite_'.$user_id,
                    'expected_amount' => $nanoAmountTon,
                    'ton_amount' => $tonAmount,
                ],
            ], 400);
        }
    }

    /**
     * Validate boost transaction with custom message format
     */
    private function validateBoostTransaction($user_id, $nanoAmountTonNeeded)
    {
        $expectedMessage = 'mining_lite_'.$user_id;
        $address = env('TON_RECEIVER_ADDRESS');

        return $this->tonHelper->validateTransactionByMessage($address, $expectedMessage, $nanoAmountTonNeeded);
    }
}
