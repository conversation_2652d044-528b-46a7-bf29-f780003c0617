<?php

namespace App\Http\Controllers\Api;

use App\Enums\CurrencyEnum;
use App\Enums\WithdrawalStatus;
use App\Http\Controllers\Controller;
use App\Models\Withdrawal;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class WithdrawalController extends Controller
{
    public function create(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:0.00000001',
            'wallet_address' => 'required|string|max:255',
            'notes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 400);
        }

        $user = $request->user();
        $amount = $request->amount;

        if ($amount > $user->ton_balance) {
            return response()->json([
                'success' => false,
                'message' => 'Insufficient balance',
            ], 400);
        }

        try {
            DB::transaction(function () use ($user, $amount, $request) {
                $user->update([
                    'ton_balance' => $user->ton_balance - $amount,
                ]);

                Withdrawal::create([
                    'user_id' => $user->id,
                    'amount' => $amount,
                    'currency' => CurrencyEnum::TON,
                    'wallet_address' => $request->wallet_address,
                    'status' => WithdrawalStatus::PENDING,
                    'notes' => $request->notes,
                ]);
            });

            return response()->json([
                'success' => true,
                'message' => 'Withdrawal request created successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create withdrawal request',
            ], 500);
        }
    }

    public function userList(Request $request)
    {
        $user = Auth::user();
        $withdrawals = Withdrawal::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return response()->json([
            'success' => true,
            'data' => $withdrawals,
        ]);
    }

    public function adminList(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'nullable|string|in:pending,approved,rejected,cancelled',
            'user_id' => 'nullable|integer|exists:users,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 400);
        }

        $query = Withdrawal::with('user');

        if ($request->status) {
            $query->where('status', $request->status);
        }

        if ($request->user_id) {
            $query->where('user_id', $request->user_id);
        }

        $withdrawals = $query->orderBy('created_at', 'desc')->paginate(20);

        return response()->json([
            'success' => true,
            'data' => $withdrawals,
        ]);
    }

    public function cancel(Request $request, $id)
    {
        $user = $request->user();

        $withdrawal = Withdrawal::where('id', $id)
            ->where('user_id', $user->id)
            ->where('status', WithdrawalStatus::PENDING)
            ->first();

        if (! $withdrawal) {
            return response()->json([
                'success' => false,
                'message' => 'Withdrawal not found or cannot be cancelled',
            ], 404);
        }

        try {
            DB::transaction(function () use ($withdrawal, $user) {
                $user->update([
                    'ton_balance' => $user->ton_balance + $withdrawal->amount,
                ]);

                $withdrawal->update([
                    'status' => WithdrawalStatus::CANCELLED,
                    'cancelled_at' => now(),
                ]);
            });

            return response()->json([
                'success' => true,
                'message' => 'Withdrawal cancelled successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to cancel withdrawal',
            ], 500);
        }
    }

    public function approve(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 400);
        }

        $withdrawal = Withdrawal::where('id', $id)
            ->where('status', WithdrawalStatus::PENDING)
            ->first();

        if (! $withdrawal) {
            return response()->json([
                'success' => false,
                'message' => 'Withdrawal not found or cannot be approved',
            ], 404);
        }

        $withdrawal->update([
            'status' => WithdrawalStatus::APPROVED,
            'admin_notes' => $request->admin_notes,
            'approved_at' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Withdrawal approved successfully',
        ]);
    }

    public function reject(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 400);
        }

        $withdrawal = Withdrawal::where('id', $id)
            ->where('status', WithdrawalStatus::PENDING)
            ->first();

        if (! $withdrawal) {
            return response()->json([
                'success' => false,
                'message' => 'Withdrawal not found or cannot be rejected',
            ], 404);
        }

        try {
            DB::transaction(function () use ($withdrawal, $request) {
                $user = $request->user();
                $user->update([
                    'ton_balance' => $user->ton_balance + $withdrawal->amount,
                ]);

                $withdrawal->update([
                    'status' => WithdrawalStatus::REJECTED,
                    'admin_notes' => $request->admin_notes,
                    'rejected_at' => now(),
                ]);
            });

            return response()->json([
                'success' => true,
                'message' => 'Withdrawal rejected successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to reject withdrawal',
            ], 500);
        }
    }
}
