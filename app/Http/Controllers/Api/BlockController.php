<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Blocked;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class BlockController extends Controller
{
    /**
     * Block a user - only accessible by staff
     */
    public function blockUser(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer|exists:users,id',
            'reason' => 'required|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $userId = $request->user_id;
        $reason = $request->reason;

        // Get staff from request attributes (set by AdminAuth middleware)
        $staff = $request->user('staff');

        // Check if user is already blocked
        $existingBlock = Blocked::where('user_id', $userId)->first();

        if ($existingBlock) {
            return response()->json([
                'success' => false,
                'message' => 'User is already blocked',
                'data' => [
                    'user_id' => $userId,
                    'current_reason' => $existingBlock->reason,
                    'blocked_at' => $existingBlock->created_at,
                    'blocked_by_staff_id' => $existingBlock->staff_id,
                ],
            ], 409);
        }

        // Get user info for response
        $user = User::find($userId);

        // Create block record
        $blocked = Blocked::create([
            'user_id' => $userId,
            'staff_id' => $staff->id,
            'reason' => $reason,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'User blocked successfully',
            'data' => [
                'block_id' => $blocked->id,
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'tele_id' => $user->tele_id,
                ],
                'reason' => $blocked->reason,
                'blocked_at' => $blocked->created_at,
                'blocked_by' => [
                    'staff_id' => $staff->id,
                    'staff_name' => $staff->username,
                ],
            ],
        ], 201);
    }

    /**
     * Unblock a user - only accessible by staff
     */
    public function unblockUser(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer|exists:users,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $userId = $request->user_id;

        // Get staff from request attributes (set by AdminAuth middleware)
        $staff = $request->user('staff');

        // Find the block record
        $blocked = Blocked::where('user_id', $userId)->first();

        if (! $blocked) {
            return response()->json([
                'success' => false,
                'message' => 'User is not blocked',
                'data' => [
                    'user_id' => $userId,
                ],
            ], 404);
        }

        // Get user info for response
        $user = User::find($userId);

        // Store block info before deletion
        $blockInfo = [
            'block_id' => $blocked->id,
            'original_reason' => $blocked->reason,
            'blocked_at' => $blocked->created_at,
            'blocked_by_staff_id' => $blocked->staff_id,
        ];

        // Delete the block record
        $blocked->delete();

        return response()->json([
            'success' => true,
            'message' => 'User unblocked successfully',
            'data' => [
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'tele_id' => $user->tele_id,
                ],
                'unblocked_at' => now(),
                'unblocked_by' => [
                    'staff_id' => $staff->id,
                    'staff_name' => $staff->username,
                ],
                'previous_block' => $blockInfo,
            ],
        ], 200);
    }

    /**
     * Get blocked users list - only accessible by staff
     */
    public function getBlockedUsers(Request $request)
    {
        $blockedUsers = Blocked::with(['user', 'staff'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return response()->json([
            'success' => true,
            'message' => 'Blocked users retrieved successfully',
            'data' => [
                'blocked_users' => $blockedUsers->items(),
                'pagination' => [
                    'current_page' => $blockedUsers->currentPage(),
                    'total_pages' => $blockedUsers->lastPage(),
                    'total_items' => $blockedUsers->total(),
                    'per_page' => $blockedUsers->perPage(),
                ],
            ],
        ], 200);
    }
}
