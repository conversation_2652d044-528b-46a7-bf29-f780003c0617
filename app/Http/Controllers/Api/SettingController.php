<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\SettingResource;
use App\Models\Setting;
use App\Services\SettingsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SettingController extends Controller
{
    /**
     * Get all public settings (accessible to all users)
     */
    public function getPublic()
    {
        $settings = Setting::getPublic();

        return response()->json([
            'success' => true,
            'message' => 'Public settings retrieved successfully',
            'data' => $settings,
        ], 200);
    }

    /**
     * Get all settings (admin only)
     */
    public function index()
    {
        $settings = Setting::orderBy('key')->get();

        return response()->json([
            'success' => true,
            'message' => 'Settings retrieved successfully',
            'data' => SettingResource::collection($settings),
        ], 200);
    }

    /**
     * Get a specific setting by key (admin only)
     */
    public function show(string $key)
    {
        $setting = Setting::where('key', $key)->first();

        if (! $setting) {
            return response()->json([
                'success' => false,
                'message' => 'Setting not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'message' => 'Setting retrieved successfully',
            'data' => new SettingResource($setting),
        ], 200);
    }

    /**
     * Update a setting (admin only)
     */
    public function update(Request $request, string $key)
    {
        $setting = Setting::where('key', $key)->first();

        if (! $setting) {
            return response()->json([
                'success' => false,
                'message' => 'Setting not found',
            ], 404);
        }

        $validator = Validator::make($request->all(), [
            'value' => 'nullable',
            'type' => 'required|in:string,number,boolean,json',
            'description' => 'nullable|string',
            'is_public' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $setting->update([
            'value' => is_array($request->value) || is_object($request->value) ? json_encode($request->value) : $request->value,
            'type' => $request->type,
            'description' => $request->description,
            'is_public' => $request->is_public ?? $setting->is_public,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Setting updated successfully',
            'data' => new SettingResource($setting),
        ], 200);
    }

    /**
     * Get admin settings (all settings for admin panel)
     *
     * @return JsonResponse
     */
    public function getAdminSettings()
    {
        try {
            $adminKeys = [];

            $settingsService = new SettingsService();
            $settings = $settingsService->getSettingsByKeys($adminKeys);

            return response()->json([
                'success' => true,
                'data' => $settings,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve admin settings',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
