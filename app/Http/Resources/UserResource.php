<?php

namespace App\Http\Resources;

use App\Services\BoostService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $service = new BoostService();
        $service->processBoostExpiration($this->resource);
        $coinsMined = $service->calculatePendingTokens($this->resource);

        return [
            'id' => $this->id,
            'tele_id' => $this->tele_id,
            'name' => $this->name,
            'username' => $this->username,
            'country' => $this->country,
            'language_code' => $this->language_code,
            'avatar_url' => $this->avatar_url,
            'last_active' => $this->last_active,
            'balance_ton' => $this->balance_ton,
            'balance_token' => $this->balance_token,
            'speed' => $this->speed,
            'pending_token' => $this->pending_token + $coinsMined,
            'last_checkpoint' => $this->last_checkpoint,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'referral_code' => $this->when(
                $this->referral_code,
                fn () => $this->referral_code,
                fn () => $this->getReferralCode()
            ),
        ];
    }
}
