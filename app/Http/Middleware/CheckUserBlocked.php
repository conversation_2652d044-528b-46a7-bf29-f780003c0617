<?php

namespace App\Http\Middleware;

use App\Models\Blocked;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckUserBlocked
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Get authenticated user (this middleware should be placed after auth:sanctum)
        $user = $request->user();

        // If no authenticated user, continue (let other middleware handle authentication)
        if (! $user) {
            return $next($request);
        }

        // Check if user is blocked
        $blocked = Blocked::where('user_id', $user->id)->first();

        if ($blocked) {
            return response()->json([
                'success' => false,
                'message' => 'Account blocked',
                'reason' => $blocked->reason,
                'blocked_at' => $blocked->created_at,
                'error_code' => 'USER_BLOCKED',
            ], 403);
        }

        return $next($request);
    }
}
