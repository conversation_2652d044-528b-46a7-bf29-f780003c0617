<?php

namespace App\Http\Middleware;

use App\Models\User;
use Closure;
use Illuminate\Support\Facades\App;
use Illuminate\Http\Request;
use Laravel\Sanctum\PersonalAccessToken;
use Symfony\Component\HttpFoundation\Response;

class UserAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user has valid token
        if (! $request->bearerToken()) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication required',
                'error_code' => 'MISSING_TOKEN',
            ], 401);
        }

        // Validate token using Laravel Sanctum
        $token = PersonalAccessToken::findToken($request->bearerToken());

        if (! $token) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid or expired token',
                'error_code' => 'INVALID_TOKEN',
            ], 401);
        }

        // Check if token is expired
        if ($token->expires_at && $token->expires_at->isPast()) {
            return response()->json([
                'success' => false,
                'message' => 'Token has expired',
                'error_code' => 'TOKEN_EXPIRED',
            ], 401);
        }

        // Get the user from the token
        $user = $token->tokenable;

        if (! $user || ! ($user instanceof User)) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid user token',
                'error_code' => 'INVALID_USER',
            ], 401);
        }

        // Check if user is blocked
        if ($user->blocked()->exists()) {
            $blockInfo = $user->blocked()->with('staff')->first();

            return response()->json([
                'success' => false,
                'message' => 'Your account has been blocked',
                'error_code' => 'USER_BLOCKED',
                'data' => [
                    'reason' => $blockInfo->reason,
                    'blocked_at' => $blockInfo->created_at,
                    'contact_support' => 'Please contact support for assistance',
                ],
            ], 403);
        }

        // Set the authenticated user
        $request->setUserResolver(function () use ($user) {
            return $user;
        });

        // Update last_active timestamp
        $user->update(['last_active' => now()]);

        // Set user's language for the request
        if (! empty($user->language_code)) {
            App::setLocale($user->language_code);
        } else {
            App::setLocale('en');
        }

        return $next($request);
    }
}
