<?php

namespace App\Console\Commands;

use Database\Seeders\TransactionSeeder;
use Illuminate\Console\Command;

class SeedTransactions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'seed:transactions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Seed transaction data for johndoe user';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Seeding transactions for johndoe user...');
        
        $this->call('db:seed', [
            '--class' => TransactionSeeder::class
        ]);
        
        $this->info('Transaction seeding completed successfully!');
        
        return Command::SUCCESS;
    }
}
