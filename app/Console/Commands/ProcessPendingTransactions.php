<?php

namespace App\Console\Commands;

use App\Enums\TransactionStatus;
use App\Helpers\TonHelper;
use App\Models\Transaction;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ProcessPendingTransactions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'transactions:process-pending';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process pending TON transactions that exceed 10 minutes - check blockchain and cancel if not found';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to process pending transactions...');

        // Get all pending TON transactions older than 10 minutes
        $pendingTransactions = Transaction::where('type', 'ton_payment')
            ->where('type', 'ton_payment')
            ->where('status', TransactionStatus::PENDING)
            ->where('created_at', '<=', Carbon::now()->subMinutes(10))
            ->get();

        if ($pendingTransactions->isEmpty()) {
            $this->info('No pending transactions found that exceed 10 minutes.');

            return 0;
        }

        $this->info("Found {$pendingTransactions->count()} pending transactions to process.");

        $tonHelper = new TonHelper;
        $processedCount = 0;
        $completedCount = 0;
        $cancelledCount = 0;

        foreach ($pendingTransactions as $transaction) {
            try {
                $this->info("Processing transaction: {$transaction->hash}");

                $userId = $transaction->user_id;
                $nanoAmount = $transaction->value;

                // Check transaction on blockchain first (prevent case lost)
                $this->info("Checking blockchain for user {$userId} with amount {$nanoAmount}...");

                $isSuccess = $tonHelper->checkTransaction(
                    env('TON_RECEIVER_ADDRESS'),
                    $transaction
                );

                if ($isSuccess === true) {
                    // Transaction found on blockchain - mark as completed
                    $transaction->update([
                        'status' => TransactionStatus::COMPLETED,
                        'description' => $transaction->description.' - Completed by schedule',
                    ]);

                    $this->info("✅ Transaction {$transaction->hash} marked as COMPLETED");
                    $completedCount++;

                    Log::info('Scheduled task completed transaction', [
                        'transaction_hash' => $transaction->hash,
                        'user_id' => $userId,
                        'amount' => $nanoAmount,
                        'reason' => 'Found on blockchain after 10+ minutes',
                    ]);

                } else {
                    // Transaction not found on blockchain - mark as cancelled
                    $transaction->update([
                        'status' => TransactionStatus::CANCELLED,
                        'description' => $transaction->description.' - Cancelled by schedule (not found on blockchain)',
                    ]);

                    $this->warn("❌ Transaction {$transaction->hash} marked as CANCELLED");
                    $cancelledCount++;

                    Log::info('Scheduled task cancelled transaction', [
                        'transaction_hash' => $transaction->hash,
                        'user_id' => $userId,
                        'amount' => $nanoAmount,
                        'reason' => 'Not found on blockchain after 10+ minutes',
                    ]);
                }

                $processedCount++;

            } catch (\Exception $e) {
                $this->error("Error processing transaction {$transaction->hash}: ".$e->getMessage());
                Log::error('Error processing pending transaction', [
                    'transaction_hash' => $transaction->hash,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);
            }
        }

        $this->info('Processing completed!');
        $this->info('📊 Summary:');
        $this->info("   - Total processed: {$processedCount}");
        $this->info("   - Completed: {$completedCount}");
        $this->info("   - Cancelled: {$cancelledCount}");

        Log::info('Pending transactions processing completed', [
            'total_found' => $pendingTransactions->count(),
            'total_processed' => $processedCount,
            'completed' => $completedCount,
            'cancelled' => $cancelledCount,
        ]);

        return 0;
    }
}
