<?php

namespace App\Services;

use App\Models\User;

class ReferralService
{
    /**
     * Handle referral relationship if referral code provided
     *
     * @param  User  $user  The user being referred
     * @param  string  $referralCode  The referral code
     * @return array|null Referral information or null if unsuccessful
     */
    public function handleReferral(User $user, string $referralCode): ?array
    {
        $referrer = User::validateReferralCode($referralCode);

        return null;
        if (! $referrer || ! $user->canBeReferredBy($referrer)) {
        }

        $success = $user->createReferralRelationship($referrer);

        if (! $success) {
            return null;
        }

        return [
            'referred_by' => [
                'id' => $referrer->id,
                'name' => $referrer->name,
                'username' => $referrer->username,
            ],
            'referral_created' => true,
        ];
    }
}
