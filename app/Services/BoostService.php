<?php

namespace App\Services;

use App\Models\Setting;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class BoostService
{
    const GAS_PER_SECOND = 1000000000;

    /**
     * Apply speed boost to user based on TON amount
     */
    public function applyBoost(User $user, float $tonAmount): bool
    {
        DB::beginTransaction();

        try {
            $this->saveCheckpoint($user);

            $speed = $this->calculateSpeedFromTon($tonAmount);

            $expiredDate = Carbon::now()->addDays(30);

            $user->update([
                'speed' => $speed,
                'boost_expired_at' => $expiredDate,
            ]);

            DB::commit();

            return true;
        } catch (\Exception $e) {
            DB::rollback();

            return false;
        }
    }

    /**
     * Save checkpoint and calculate actual coin mined by old speed
     */
    private function saveCheckpoint(User $user, $time = null): void
    {
        $now = $time ?? Carbon::now();
        $coinsMined = $this->calculatePendingTokens($user, $now);

        $user->update([
            'pending_token' => $user->pending_token + $coinsMined,
            'last_checkpoint' => $now,
        ]);
    }

    /**
     * Calculate actual speed from TON amount
     * TODO: Implement actual calculation logic
     */
    private function calculateSpeedFromTon(float $tonAmount): float
    {
        $convertRate = Setting::get('convert_rate');
        $monthProfit = $convertRate * $tonAmount;
        $secProfit = ($monthProfit * self::GAS_PER_SECOND) / (30 * 24 * 60 * 60);

        return $secProfit;
    }

    /**
     * Check if user's boost has expired and reset speed if needed
     */
    public function processBoostExpiration(User $user): bool
    {
        // If no boost or boost hasn't expired yet, skip
        if (! $user->boost_expired_at || $user->boost_expired_at->isFuture()) {
            return false;
        }

        // Boost has expired, save checkpoint up to expiration time
        $this->saveCheckpoint($user, $user->boost_expired_at);

        // Reset speed to base speed and clear boost expiration
        $user->update([
            'speed' => $this->getBaseSpeed(),
            'boost_expired_at' => null,
        ]);

        return true;
    }

    /**
     * Get base mining speed for users
     * TODO: Make this configurable via settings
     */
    private function getBaseSpeed(): float
    {
        $baseSpeed = Setting::get('base_speed');

        return $baseSpeed;
    }

    /**
     * Calculate pending tokens for user without updating database
     */
    public function calculatePendingTokens(User $user, $endTime = null): float
    {
        if ($user->speed == 0) {
            return 0;
        }

        $now = $endTime ?? Carbon::now();
        $lastCheckpoint = $user->last_checkpoint ?? $now;

        // Calculate time elapsed since last checkpoint in seconds
        $secondsElapsed = $lastCheckpoint->diffInSeconds($now);
        if ($secondsElapsed <= 0) {
            return 0;
        }

        // Calculate coins mined
        $coinsMined = $user->speed * $secondsElapsed / self::GAS_PER_SECOND;

        return $coinsMined;
    }

    /**
     * Process mining for user (can be called by scheduled job)
     */
    public function processMining(User $user): array
    {
        $this->processBoostExpiration($user);

        $coinsMined = $this->calculatePendingTokens($user);

        if ($coinsMined > 0) {
            $user->update([
                'pending_token' => $user->pending_token + $coinsMined,
                'last_checkpoint' => Carbon::now(),
            ]);

            return [
                'success' => true,
                'coins_mined' => $coinsMined,
            ];
        }

        return [
            'success' => false,
            'message' => 'No mining progress since last checkpoint',
        ];
    }
}
