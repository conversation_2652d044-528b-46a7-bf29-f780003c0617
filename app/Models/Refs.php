<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Refs extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'ref_id',
        'commission',
    ];

    protected function casts(): array
    {
        return [
            'commission' => 'decimal:8',
        ];
    }

    // Relationships
    public function referrer()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function referred()
    {
        return $this->belongsTo(User::class, 'ref_id');
    }

    // Scopes

    /**
     * Scope to get referrals by a specific user
     */
    public function scopeByReferrer($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to get referrals for a specific user
     */
    public function scopeByReferred($query, $userId)
    {
        return $query->where('ref_id', $userId);
    }

    /**
     * Scope to get referrals with commission greater than zero
     */
    public function scopeWithCommission($query)
    {
        return $query->where('commission', '>', 0);
    }

    // Validation methods

    /**
     * Check if a referral relationship already exists
     */
    public static function relationshipExists(int $referrerId, int $referredId): bool
    {
        return self::where('user_id', $referrerId)
            ->where('ref_id', $referredId)
            ->exists();
    }

    /**
     * Validate referral relationship before creation
     */
    public static function validateReferralRelationship(int $referrerId, int $referredId): array
    {
        $errors = [];

        // Check if users are the same
        if ($referrerId === $referredId) {
            $errors[] = 'Users cannot refer themselves';
        }

        // Check if relationship already exists
        if (self::relationshipExists($referrerId, $referredId)) {
            $errors[] = 'Referral relationship already exists';
        }

        // Check if referred user is already referred by someone else
        if (self::where('ref_id', $referredId)->exists()) {
            $errors[] = 'User is already referred by someone else';
        }

        return $errors;
    }

    /**
     * Create a referral relationship with validation
     */
    public static function createReferral(int $referrerId, int $referredId, float $commission = 0): array
    {
        $errors = self::validateReferralRelationship($referrerId, $referredId);

        if (! empty($errors)) {
            return [
                'success' => false,
                'errors' => $errors,
                'referral' => null,
            ];
        }

        $referral = self::create([
            'user_id' => $referrerId,
            'ref_id' => $referredId,
            'commission' => $commission,
        ]);

        return [
            'success' => true,
            'errors' => [],
            'referral' => $referral,
        ];
    }

    // Helper methods

    /**
     * Calculate total commission for a referrer
     */
    public static function getTotalCommissionForReferrer(int $referrerId): float
    {
        return self::where('user_id', $referrerId)->sum('commission');
    }

    /**
     * Get referral count for a referrer
     */
    public static function getReferralCountForReferrer(int $referrerId): int
    {
        return self::where('user_id', $referrerId)->count();
    }

    /**
     * Get recent referrals for a referrer
     */
    public static function getRecentReferralsForReferrer(int $referrerId, int $limit = 10)
    {
        return self::where('user_id', $referrerId)
            ->with(['referred'])
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Update commission for a referral
     */
    public function updateCommission(float $amount): bool
    {
        return $this->update(['commission' => $this->commission + $amount]);
    }
}
