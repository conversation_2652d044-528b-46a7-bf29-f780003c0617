<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'country',
        'tele_id',
        'name',
        'username',
        'language_code',
        'avatar_url',
        'last_active',
        'balance_ton',
        'balance_token',
        'referral_code',
        'speed',
        'pending_token',
        'last_checkpoint',
        'boost_expired_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'last_active' => 'datetime',
            'balance_ton' => 'decimal:8',
            'balance_token' => 'decimal:8',
            'speed' => 'decimal:8',
            'pending_token' => 'decimal:8',
            'last_checkpoint' => 'datetime',
            'boost_expired_at' => 'datetime',
        ];
    }

    public function transactions()
    {
        return $this->hasMany(Transaction::class);
    }

    public function blocked()
    {
        return $this->hasOne(Blocked::class);
    }

    // Referral relationships
    public function referrals()
    {
        return $this->hasMany(Refs::class, 'user_id'); // users I referred
    }

    public function referredBy()
    {
        return $this->hasOne(Refs::class, 'ref_id'); // who referred me
    }

    // Referral methods

    /**
     * Generate a unique referral code for the user
     */
    public function generateReferralCode(): string
    {
        do {
            $code = $this->generateRandomCode();
        } while (self::where('referral_code', $code)->exists());

        $this->update(['referral_code' => $code]);

        return $code;
    }

    /**
     * Generate a random 6-character alphanumeric code
     */
    private function generateRandomCode(): string
    {
        $characters = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789'; // Exclude confusing characters
        $code = '';

        for ($i = 0; $i < 6; $i++) {
            $code .= $characters[random_int(0, strlen($characters) - 1)];
        }

        return $code;
    }

    /**
     * Get the user's referral code, generating one if it doesn't exist
     */
    public function getReferralCode(): string
    {
        if (empty($this->referral_code)) {
            return $this->generateReferralCode();
        }

        return $this->referral_code;
    }

    /**
     * Validate a referral code and return the referrer user
     */
    public static function validateReferralCode(string $code): ?self
    {
        if (empty($code)) {
            return null;
        }

        return self::where('referral_code', strtoupper($code))->first();
    }

    /**
     * Check if this user can be referred by another user
     */
    public function canBeReferredBy(self $referrer): bool
    {
        // Can't refer yourself
        if ($this->id === $referrer->id) {
            return false;
        }

        // Can't be referred if already referred
        if ($this->referredBy()->exists()) {
            return false;
        }

        return true;
    }

    /**
     * Create a referral relationship
     */
    public function createReferralRelationship(self $referrer): bool
    {
        if (! $this->canBeReferredBy($referrer)) {
            return false;
        }

        Refs::create([
            'user_id' => $referrer->id,
            'ref_id' => $this->id,
            'commission' => 0, // Will be calculated later
        ]);

        return true;
    }

    /**
     * Get referral statistics for the user
     */
    public function getReferralStats(): array
    {
        $totalReferrals = $this->referrals()->count();
        $totalCommission = $this->referrals()->sum('commission');

        return [
            'total_referrals' => $totalReferrals,
            'total_commission' => $totalCommission,
            'referral_code' => $this->getReferralCode(),
            'referral_link' => $this->getReferralLink(),
        ];
    }

    // Telegram-specific methods

    /**
     * Get the user's display name for Telegram
     */
    public function getDisplayNameAttribute(): string
    {
        if ($this->username) {
            return "@{$this->username}";
        }

        return $this->name ?: "User {$this->tele_id}";
    }

    /**
     * Check if user is active (seen recently)
     */
    public function isActive(): bool
    {
        if (!$this->last_active) {
            return false;
        }

        return $this->last_active->diffInDays(now()) <= 30;
    }

    /**
     * Update last seen timestamp
     */
    public function updateLastSeen(): void
    {
        $this->update(['last_active' => now()]);
    }

    /**
     * Scope to get only active users
     */
    public function scopeActive($query)
    {
        return $query->where('last_active', '>=', now()->subDays(30));
    }

    /**
     * Scope to get only real users (not bots) - for now, all users are real users
     */
    public function scopeRealUsers($query)
    {
        return $query; // All users in this system are real users
    }

    public function addTonBalance(float $amount): void
    {
        $this->balance_ton += $amount;
        $this->save();
    }
}
