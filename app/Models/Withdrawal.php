<?php

namespace App\Models;

use App\Enums\WithdrawalStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Withdrawal extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'amount',
        'currency',
        'wallet_address',
        'status',
        'notes',
        'admin_notes',
        'approved_at',
        'rejected_at',
        'cancelled_at',
    ];

    protected function casts(): array
    {
        return [
            'amount' => 'decimal:8',
            'status' => WithdrawalStatus::class,
            'approved_at' => 'datetime',
            'rejected_at' => 'datetime',
            'cancelled_at' => 'datetime',
        ];
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function scopePending($query)
    {
        return $query->where('status', WithdrawalStatus::PENDING);
    }

    public function scopeApproved($query)
    {
        return $query->where('status', WithdrawalStatus::APPROVED);
    }

    public function scopeRejected($query)
    {
        return $query->where('status', WithdrawalStatus::REJECTED);
    }

    public function scopeCancelled($query)
    {
        return $query->where('status', WithdrawalStatus::CANCELLED);
    }
}
