<?php

namespace App\Models;

use App\Enums\TransactionStatus;
use App\Enums\TransactionType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Transaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'type',
        'value',
        'description',
        'user_id',
        'staff_id',
        'status',
        'hash',
    ];

    protected $casts = [
        'status' => TransactionStatus::class,
        'type' => TransactionType::class,
    ];

    /**
     * Generate a unique hash for the transaction
     */
    public static function generateHash(): string
    {
        do {
            $hash = 'tx_'.Str::random(32);
        } while (self::where('hash', $hash)->exists());

        return $hash;
    }

    // Relationships
    public function transactionable()
    {
        return $this->morphTo();
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function staff()
    {
        return $this->belongsTo(Staff::class);
    }

    /**
     * Get the transaction group for this transaction
     */
    public function getGroupAttribute(): string
    {
        return TransactionType::getGroupForType($this->type);
    }
}
