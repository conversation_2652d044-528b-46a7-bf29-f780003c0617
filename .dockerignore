# Dependencies
node_modules/
# vendor/ - We'll let composer handle this

# Build artifacts
/public/hot
/public/storage
/storage/*.key

# Environment files
.env
.env.backup
.env.production

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Docker
Dockerfile
.dockerignore
docker-compose*.yml

# Testing
.phpunit.result.cache
/coverage/

# Logs
*.log
/storage/logs/*.log

# Cache
/bootstrap/cache/*.php
/storage/framework/cache/*
/storage/framework/sessions/*
/storage/framework/views/*

# Temporary files
*.tmp
*.temp
