<?php

use Illuminate\Support\Facades\Route;

// Web routes disabled for API-only application
// All API routes are defined in routes/api.php

// Only catch non-API routes with the fallback
Route::fallback(function () {
    // Check if the request is for an API route
    if (request()->is('api/*')) {
        // Let API routes handle their own 404s
        abort(404, 'API endpoint not found');
    }

    return response()->json([
        'error' => 'Route not found. This is an API-only application.',
        'message' => 'Please use /api/ prefix for all API endpoints.',
        'available_endpoints' => [
            'GET /api/health' => 'API health check',
            'GET /api/v1/*' => 'API v1 endpoints',
            'GET /api/module-test' => 'Module system test',
            'GET /api/user-management-test' => 'UserManagement module test',
        ],
    ], 404);
});
