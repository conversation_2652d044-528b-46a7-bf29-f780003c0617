# JWT-like Authentication with Laravel Sanctum

This Laravel application implements JWT-like token authentication using Laravel Sanctum, providing passwordless authentication for users.

## Features

- **Passwordless Authentication**: Users don't need passwords - authentication is based on Telegram ID
- **Token-based Security**: Uses Laravel Sanctum for secure API token management
- **Auto-expiring Tokens**: Tokens expire after 30 days by default
- **Device Management**: Supports multiple devices per user
- **Token Refresh**: Ability to refresh tokens before expiration
- **Logout Options**: Single device logout or logout from all devices

## API Endpoints

### Authentication Routes

#### 1. User Sync/Login (No Authentication Required)
```http
POST /api/auth/sync
```

**Request Body:**
```json
{
    "telegram_id": "123456789",
    "name": "John Doe",
    "country": "US",
    "language_code": "en",
    "device_name": "mobile-app" // optional
}
```

**Response (New User):**
```json
{
    "success": true,
    "message": "User initialized successfully",
    "data": {
        "user": {
            "id": 1,
            "tele_id": "123456789",
            "name": "<PERSON>e",
            "country": "US",
            "language_code": "en",
            "avatar_url": null,
            "last_active": "2024-01-15T10:30:00.000000Z",
            "balance_ton": "0.00000000",
            "balance_token": "0.00000000",
            "created_at": "2024-01-15T10:30:00.000000Z",
            "updated_at": "2024-01-15T10:30:00.000000Z"
        },
        "token": "1|abc123def456...", // Use this token for authenticated requests
        "token_expires_at": "2024-02-14T10:30:00.000000Z",
        "action": "initialized"
    }
}
```

**Response (Existing User):**
```json
{
    "success": true,
    "message": "User restored successfully",
    "data": {
        "user": { /* user data */ },
        "token": "2|xyz789abc123...", // New token issued
        "token_expires_at": "2024-02-14T10:30:00.000000Z",
        "action": "restored"
    }
}
```

#### 2. Get User Profile (Authentication Required)
```http
GET /api/auth/profile
Authorization: Bearer {token}
```

#### 3. Refresh Token (Authentication Required)
```http
POST /api/auth/refresh-token
Authorization: Bearer {token}
```

**Request Body:**
```json
{
    "device_name": "mobile-app" // optional
}
```

#### 4. Logout (Authentication Required)
```http
POST /api/auth/logout
Authorization: Bearer {token}
```

#### 5. Logout from All Devices (Authentication Required)
```http
POST /api/auth/logout-all
Authorization: Bearer {token}
```

### Protected User Routes

All routes under `/api/user/*` require authentication:

```http
GET /api/user/profile
POST /api/user/ton-payment/init
Authorization: Bearer {token}
```

### Test Endpoints

#### Public Test (No Authentication)
```http
GET /api/public/test
```

#### Authenticated Test (Authentication Required)
```http
GET /api/auth/test
Authorization: Bearer {token}
```

## Implementation Details

### User Model
- Uses `Laravel\Sanctum\HasApiTokens` trait
- No password field required
- Unique identification via `tele_id` (Telegram ID)

### Middleware
- `UserAuth` middleware validates Sanctum tokens
- Checks for token expiration
- Verifies user is not blocked
- Sets authenticated user in request context

### Token Management
- Tokens expire after 30 days
- Previous tokens for the same device are revoked when new ones are created
- Tokens include full abilities (`['*']`)
- Each token is associated with a device name

### Security Features
- Bearer token validation
- Automatic token expiration
- User blocking support
- Device-specific token management

## Usage Examples

### Frontend Integration

#### 1. Initial Authentication
```javascript
const syncUser = async (telegramData) => {
    const response = await fetch('/api/auth/sync', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            telegram_id: telegramData.id.toString(),
            name: telegramData.first_name + ' ' + telegramData.last_name,
            country: telegramData.country_code,
            language_code: telegramData.language_code,
            device_name: 'web-app'
        })
    });

    const data = await response.json();

    if (data.success) {
        // Store token for future requests
        localStorage.setItem('auth_token', data.data.token);
        return data.data;
    }

    throw new Error(data.message);
};
```

#### 2. Making Authenticated Requests
```javascript
const makeAuthenticatedRequest = async (endpoint, options = {}) => {
    const token = localStorage.getItem('auth_token');

    const response = await fetch(endpoint, {
        ...options,
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
            ...options.headers
        }
    });

    // Handle token expiration
    if (response.status === 401) {
        localStorage.removeItem('auth_token');
        // Redirect to login or refresh token
        return;
    }

    return response.json();
};

// Example usage
const getUserProfile = () => makeAuthenticatedRequest('/api/auth/profile');
const initTonPayment = (data) => makeAuthenticatedRequest('/api/user/ton-payment/init', {
    method: 'POST',
    body: JSON.stringify(data)
});
```

#### 3. Token Refresh
```javascript
const refreshToken = async () => {
    try {
        const data = await makeAuthenticatedRequest('/api/auth/refresh-token', {
            method: 'POST',
            body: JSON.stringify({ device_name: 'web-app' })
        });

        localStorage.setItem('auth_token', data.data.token);
        return data.data.token;
    } catch (error) {
        // Handle refresh failure
        localStorage.removeItem('auth_token');
        throw error;
    }
};
```

### Mobile App Integration

#### React Native Example
```javascript
import AsyncStorage from '@react-native-async-storage/async-storage';

class AuthService {
    static async syncUser(telegramData) {
        const response = await fetch('/api/auth/sync', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                ...telegramData,
                device_name: 'react-native-app'
            })
        });

        const data = await response.json();

        if (data.success) {
            await AsyncStorage.setItem('auth_token', data.data.token);
            return data.data;
        }

        throw new Error(data.message);
    }

    static async makeAuthenticatedRequest(endpoint, options = {}) {
        const token = await AsyncStorage.getItem('auth_token');

        const response = await fetch(endpoint, {
            ...options,
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json',
                ...options.headers
            }
        });

        if (response.status === 401) {
            await AsyncStorage.removeItem('auth_token');
            // Handle unauthorized access
        }

        return response.json();
    }
}
```

## Configuration

### Environment Variables
```env
# Laravel Sanctum token expiration (in minutes, null for no expiration)
SANCTUM_EXPIRATION=43200  # 30 days

# Sanctum stateful domains for SPA authentication
SANCTUM_STATEFUL_DOMAINS=localhost,127.0.0.1
```

### Auth Configuration
The authentication guard is configured to use Sanctum by default:

```php
// config/auth.php
'defaults' => [
    'guard' => env('AUTH_GUARD', 'sanctum'),
],
'guards' => [
    'sanctum' => [
        'driver' => 'sanctum',
        'provider' => 'users',
    ],
],
```

## Error Handling

### Common Error Responses

#### 401 Unauthorized
```json
{
    "success": false,
    "message": "Bearer token required"
}
```

```json
{
    "success": false,
    "message": "Invalid token"
}
```

```json
{
    "success": false,
    "message": "Token expired"
}
```

#### 403 Forbidden
```json
{
    "success": false,
    "message": "User is blocked"
}
```

#### 422 Validation Error
```json
{
    "success": false,
    "message": "Validation failed",
    "errors": {
        "telegram_id": ["The telegram id field is required."],
        "name": ["The name field is required."]
    }
}
```

## Best Practices

1. **Token Storage**: Store tokens securely (localStorage for web, secure storage for mobile)
2. **Token Refresh**: Implement automatic token refresh before expiration
3. **Error Handling**: Always handle 401 responses by clearing stored tokens
4. **Device Names**: Use descriptive device names for better token management
5. **Logout**: Always call logout endpoints when user explicitly logs out
6. **Security**: Never expose tokens in URLs or logs

## Database Schema

The authentication system uses Laravel Sanctum's `personal_access_tokens` table:

```sql
CREATE TABLE personal_access_tokens (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    tokenable_type VARCHAR(255) NOT NULL,
    tokenable_id BIGINT NOT NULL,
    name VARCHAR(255) NOT NULL,
    token VARCHAR(64) NOT NULL UNIQUE,
    abilities TEXT,
    last_used_at TIMESTAMP NULL,
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    INDEX personal_access_tokens_tokenable_type_tokenable_id_index (tokenable_type, tokenable_id)
);
```

Users are identified by their `tele_id` field in the `users` table, no password required.
