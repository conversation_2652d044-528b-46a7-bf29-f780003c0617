# Mining Claimed API Documentation

## Overview
The Mining Claimed API endpoint allows authenticated users to claim their pending mining tokens and add them to their main token balance.

## Endpoint Details

### URL
```
POST /api/user/mining/claimed
```

### Authentication
- **Required**: Yes
- **Middleware**: `user.auth`
- **Type**: User authentication (Telegram-based)

### Request Format

#### Headers
```
Authorization: Bearer <user_token>
Content-Type: application/json
```

#### Body
No request body required.

### Response Format

#### Success Response (200 OK)
```json
{
  "success": true,
  "message": "Mining tokens claimed successfully",
  "data": {
    "claimed_amount": 25.5,
    "new_balance": 125.75,
    "pending_token": 0,
    "claimed_at": "2024-01-15T10:30:00Z"
  }
}
```

**Response Fields:**
- `claimed_amount`: Number of tokens that were claimed
- `new_balance`: User's total token balance after claiming
- `pending_token`: Always 0 after successful claim
- `claimed_at`: Timestamp when tokens were claimed

#### Error Responses

**Insufficient Pending Tokens (400 Bad Request)**
```json
{
  "success": false,
  "message": "You need to have at least 1 pending token to claim",
  "data": {
    "pending_token": 0.5,
    "current_balance": 100.25
  }
}
```

**Server Error (500 Internal Server Error)**
```json
{
  "success": false,
  "message": "Failed to claim mining tokens"
}
```

## Business Logic

### Validation Rules
1. User must have more than 1 pending token to claim
2. User must be authenticated via user.auth middleware

### Process Flow
1. **Authentication Check**: Validates user authentication token
2. **Pending Token Check**: Verifies user has sufficient pending tokens (>1)
3. **Balance Update**: Adds pending tokens to main token balance
4. **State Reset**: Sets pending tokens to 0
5. **Checkpoint Update**: Updates `last_checkpoint` to current timestamp
6. **Logging**: Records successful claims and errors for monitoring

### Database Changes
The following user fields are updated:
- `token_balance`: Incremented by `pending_token` amount
- `pending_token`: Reset to 0
- `last_checkpoint`: Updated to current timestamp

## Security Features

- Protected by user authentication middleware
- Validates minimum claim amount (1 token)
- Comprehensive error logging for monitoring and debugging
- Transaction-safe database updates

## Example Usage

### cURL Request
```bash
curl -X POST \
  https://api.example.com/api/user/mining/claimed \
  -H "Authorization: Bearer <user_token>" \
  -H "Content-Type: application/json"
```

## Error Handling

The endpoint includes comprehensive error handling:

1. **Validation Errors**: Returns 400 status with detailed error message
2. **Server Errors**: Returns 500 status with generic error message
3. **All errors are logged** with user ID and error details for debugging

## Rate Limiting

This endpoint is subject to the application's standard rate limiting rules as defined in the Laravel configuration.

## Monitoring

All mining token claims are logged with the following information:
- User ID
- Claimed amount
- New balance
- Timestamp
- Any errors that occur

This logging enables monitoring of user activity and troubleshooting issues.
