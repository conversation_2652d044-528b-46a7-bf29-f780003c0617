# Mining API Documentation

## Overview
The Mining API provides endpoints for managing user mining operations. Currently includes the ability to claim accumulated mining tokens from pending balance.

## Endpoint

### POST /api/user/mining/claimed

**Description:** Claim all pending mining tokens and transfer them to user's main balance

**Authentication:** Required (user.auth middleware)

**Content-Type:** application/json

## Request Parameters

This endpoint requires no request parameters.

## Request Example

```json
{}
```

## Process Flow

The system performs the following operations:

1. **Validation**: Checks if user has pending tokens (minimum 1 token required)
2. **Balance Transfer**: Moves all `pending_token` to `token_balance`
3. **Checkpoint Reset**: Sets `pending_token` to 0 and `last_checkpoint` to current time
4. **Logging**: Records the claim operation for audit purposes

## Response Format

### Success Response (200)

```json
{
  "success": true,
  "message": "Mining tokens claimed successfully",
  "data": {
    "claimed_amount": 1234.56,
    "new_balance": 5678.90,
    "pending_token": 0,
    "claimed_at": "2025-07-14T13:44:28.000000Z"
  }
}
```

### Insufficient Tokens Response (400)

```json
{
  "success": false,
  "message": "You need to have at least 1 pending token to claim",
  "data": {
    "pending_token": 0.5,
    "current_balance": 1000.00
  }
}
```

### Server Error Response (500)

```json
{
  "success": false,
  "message": "Failed to claim mining tokens"
}
```

## Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `success` | boolean | Operation success status |
| `message` | string | Human-readable status message |
| `claimed_amount` | numeric | Amount of tokens claimed from pending |
| `new_balance` | numeric | User's total token balance after claim |
| `pending_token` | numeric | Remaining pending tokens (always 0 after successful claim) |
| `claimed_at` | datetime | Timestamp when tokens were claimed |
| `current_balance` | numeric | User's current token balance (in error responses) |

## Implementation Details

### Minimum Claim Amount
- Users must have at least 1 pending token to claim
- This prevents unnecessary database operations for minimal amounts

### Database Operations
- **Atomic Transaction**: All database updates wrapped in transaction
- **Balance Update**: `token_balance` increased by `pending_token` amount
- **Checkpoint Reset**: `pending_token` set to 0, `last_checkpoint` updated to now

### Logging
- Successful claims are logged with user ID, amount, and new balance
- Failed operations are logged with error details
- Logs are stored for audit and monitoring purposes

## Mining System Integration

### Checkpoint System
- `last_checkpoint` tracks when user last claimed or had mining calculated
- Mining speed calculations use time elapsed since last checkpoint
- Claiming resets checkpoint to current time for accurate future calculations

### Token Accumulation
- Pending tokens accumulate based on user's mining speed over time
- Speed can be boosted through the Boost API
- Base speed is configurable via system settings

## Error Handling

The endpoint handles the following error scenarios:

1. **Insufficient Tokens**: Returns 400 when pending tokens < 1
2. **Database Errors**: Returns 500 with rollback on transaction failure
3. **Authentication Errors**: Handled by middleware (401/403)

## Security Considerations

- User authentication required via middleware
- Database transactions ensure data consistency
- All operations are logged for audit trail
- No external dependencies reduce attack surface

## Integration Example

```javascript
// Example client-side integration
const response = await fetch('/api/user/mining/claimed', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + userToken
  },
  body: JSON.stringify({})
});

const result = await response.json();
if (result.success) {
  console.log('Claimed:', result.data.claimed_amount);
  console.log('New Balance:', result.data.new_balance);
} else {
  console.error('Claim failed:', result.message);
}
```

## Related Models

- **User Model**: Stores `token_balance`, `pending_token`, and `last_checkpoint`
- **Mining System**: Calculates pending tokens based on speed and time
- **Boost System**: Affects mining speed used in calculations

## Future Enhancements

Potential future additions to the Mining API:

- **GET /api/user/mining/status**: Get current mining statistics
- **POST /api/user/mining/start**: Start/stop mining operations
- **GET /api/user/mining/history**: View claim history