# Laravel Modules Development Guide

This guide explains how to use the nwidart/laravel-modules package for modular development in this Laravel project.

## Overview

The project now uses a modular architecture where features are organized into self-contained modules. Each module has its own:
- Controllers, Models, Services
- Routes (API and Web)
- Migrations and Seeders
- Views and Assets
- Tests
- Configuration

## Module Structure

```
Modules/
└── ModuleName/
    ├── app/
    │   ├── Http/
    │   │   ├── Controllers/
    │   │   └── Requests/
    │   ├── Models/
    │   ├── Providers/
    │   ├── Services/
    │   └── Transformers/
    ├── config/
    ├── database/
    │   ├── factories/
    │   ├── migrations/
    │   └── seeders/
    ├── resources/
    │   ├── assets/
    │   └── views/
    ├── routes/
    │   ├── api.php
    │   └── web.php
    ├── tests/
    ├── composer.json
    ├── module.json
    └── package.json
```

## Getting Started

### Creating a New Module

```bash
# Create a new module
docker-compose exec backend php artisan module:make ModuleName

# List all modules
docker-compose exec backend php artisan module:list

# Enable/disable a module
docker-compose exec backend php artisan module:enable ModuleName
docker-compose exec backend php artisan module:disable ModuleName
```

### Generating Components

```bash
# Generate a model
docker-compose exec backend php artisan module:make-model ModelName ModuleName

# Generate a controller
docker-compose exec backend php artisan module:make-controller ControllerName ModuleName

# Generate an API controller
docker-compose exec backend php artisan module:make-controller ControllerName ModuleName --api

# Generate a migration
docker-compose exec backend php artisan module:make-migration create_table_name ModuleName

# Generate a request class
docker-compose exec backend php artisan module:make-request RequestName ModuleName

# Generate a resource class
docker-compose exec backend php artisan module:make-resource ResourceName ModuleName

# Generate a service class
docker-compose exec backend php artisan module:make-service ServiceName ModuleName

# Generate a seeder
docker-compose exec backend php artisan module:make-seeder SeederName ModuleName
```

## Example: UserManagement Module

The project includes a complete example module called `UserManagement` that demonstrates:

### 1. Model with Relationships
```php
// Modules/UserManagement/app/Models/UserProfile.php
class UserProfile extends Model
{
    protected $fillable = [
        'user_id', 'first_name', 'last_name', 'phone', 
        'date_of_birth', 'bio', 'avatar', 'preferences'
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
```

### 2. Service Layer
```php
// Modules/UserManagement/app/Services/UserProfileService.php
class UserProfileService
{
    public function createProfile(array $data): UserProfile
    {
        return UserProfile::create($data);
    }
    
    // ... other methods
}
```

### 3. API Controller with Dependency Injection
```php
// Modules/UserManagement/app/Http/Controllers/UserProfileController.php
class UserProfileController extends Controller
{
    public function __construct(
        private UserProfileService $userProfileService
    ) {}
    
    public function store(UserProfileRequest $request): JsonResponse
    {
        $profile = $this->userProfileService->createProfile($request->validated());
        return response()->json([
            'data' => new UserProfileResource($profile),
            'message' => 'User profile created successfully'
        ], 201);
    }
}
```

### 4. API Routes
```php
// Modules/UserManagement/routes/api.php
Route::middleware(['auth:sanctum'])->prefix('v1')->group(function () {
    Route::apiResource('usermanagements', UserManagementController::class);
});
```

## Best Practices

### 1. Module Organization
- Keep modules focused on a single domain/feature
- Use descriptive module names (e.g., UserManagement, OrderProcessing)
- Follow Laravel naming conventions within modules

### 2. Dependencies
- Modules should be as independent as possible
- Use interfaces for cross-module communication
- Avoid direct dependencies between modules

### 3. Database
- Each module should manage its own database tables
- Use migrations within the module for schema changes
- Consider using module-specific table prefixes if needed

### 4. API Design
- Use consistent API patterns across modules
- Implement proper error handling and validation
- Use API resources for response formatting

### 5. Testing
- Write tests within each module
- Test module functionality in isolation
- Use factories and seeders for test data

## Running Migrations

```bash
# Run all migrations (including module migrations)
docker-compose exec backend php artisan migrate

# Run migrations for a specific module
docker-compose exec backend php artisan module:migrate ModuleName

# Rollback module migrations
docker-compose exec backend php artisan module:migrate-rollback ModuleName
```

## Configuration

Module configuration is managed in `config/modules.php`. Key settings:

- `paths.modules`: Where modules are stored (default: `Modules/`)
- `auto-discover.migrations`: Automatically register module migrations
- `generator`: Control which components are generated by default

## Autoloading

Modules use their own `composer.json` for autoloading. The main application's `composer.json` includes module autoloading via the merge plugin.

## Troubleshooting

### Routes Not Loading
1. Clear route cache: `php artisan route:clear`
2. Check module is enabled: `php artisan module:list`
3. Verify RouteServiceProvider is registered in module's ServiceProvider

### Classes Not Found
1. Run `composer dump-autoload`
2. Check namespace declarations match file structure
3. Verify module's composer.json autoloading configuration

### Migrations Not Running
1. Ensure `auto-discover.migrations` is true in config/modules.php
2. Check migration file naming follows Laravel conventions
3. Verify module is enabled

## Next Steps

1. Create your first module using the commands above
2. Follow the UserManagement module as a template
3. Implement your business logic within the module structure
4. Write tests for your module functionality
5. Document your module's API and usage
