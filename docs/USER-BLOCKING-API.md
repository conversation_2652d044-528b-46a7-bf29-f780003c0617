# User Blocking API Documentation

## Overview
The User Blocking API provides endpoints for administrators to block and unblock users from accessing the system. When a user is blocked, they cannot access any authenticated endpoints and receive specific error messages with clear error codes for client-side handling.

## Base URL
```
/api/admin/users/
```

## Authentication
All endpoints require admin authentication (AdminAuth middleware).

## Middleware Protection
The `UserAuth` middleware automatically checks if a user is blocked before allowing access to any user-protected endpoints. Blocked users receive immediate rejection with detailed error information.

## Endpoints

### 1. Block User
**POST** `/admin/users/block`

Blocks a user from accessing the system and revokes all their active tokens.

#### Request Body
```json
{
    "user_id": 123,
    "reason": "Violation of terms of service"
}
```

#### Request Parameters
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `user_id` | integer | Yes | ID of the user to block |
| `reason` | string | Yes | Reason for blocking (max: 500 chars) |

#### Response
**Success (201)**
```json
{
    "success": true,
    "message": "User blocked successfully",
    "data": {
        "block_id": 1,
        "user": {
            "id": 123,
            "name": "John Doe",
            "tele_id": "*********",
            "username": "johndoe"
        },
        "reason": "Violation of terms of service",
        "blocked_at": "2025-07-14T12:30:00.000000Z",
        "blocked_by_staff_id": 1,
        "tokens_revoked": true
    }
}
```

**Error Responses**

*User Already Blocked (409)*
```json
{
    "success": false,
    "message": "User is already blocked",
    "error_code": "USER_ALREADY_BLOCKED",
    "data": {
        "user_id": 123,
        "current_reason": "Previous violation",
        "blocked_at": "2025-07-14T10:00:00.000000Z",
        "blocked_by_staff_id": 1
    }
}
```

*User Not Found (404)*
```json
{
    "success": false,
    "message": "User not found",
    "error_code": "USER_NOT_FOUND"
}
```

*Validation Error (422)*
```json
{
    "success": false,
    "message": "Validation failed",
    "errors": {
        "user_id": ["The user id field is required."],
        "reason": ["The reason field is required."]
    }
}
```

#### Features
- **Immediate Token Revocation**: All user tokens are revoked instantly
- **Duplicate Protection**: Prevents blocking an already blocked user
- **Audit Trail**: Records who blocked the user and when
- **Validation**: Ensures user exists and reason is provided

---

### 2. Unblock User
**POST** `/admin/users/unblock`

Unblocks a user, allowing them to access the system again.

#### Request Body
```json
{
    "user_id": 123
}
```

#### Request Parameters
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `user_id` | integer | Yes | ID of the user to unblock |

#### Response
**Success (200)**
```json
{
    "success": true,
    "message": "User unblocked successfully",
    "data": {
        "user": {
            "id": 123,
            "name": "John Doe",
            "tele_id": "*********",
            "username": "johndoe"
        },
        "unblocked_at": "2025-07-14T13:00:00.000000Z",
        "unblocked_by_staff_id": 1,
        "previous_block": {
            "block_id": 1,
            "original_reason": "Violation of terms of service",
            "blocked_at": "2025-07-14T12:30:00.000000Z",
            "blocked_by_staff_id": 1
        }
    }
}
```

**Error Responses**

*User Not Blocked (404)*
```json
{
    "success": false,
    "message": "User is not blocked",
    "error_code": "USER_NOT_BLOCKED",
    "data": {
        "user_id": 123
    }
}
```

*User Not Found (404)*
```json
{
    "success": false,
    "message": "User not found",
    "error_code": "USER_NOT_FOUND"
}
```

#### Features
- **Historical Data**: Preserves information about the previous block
- **Audit Trail**: Records who unblocked the user and when
- **Instant Effect**: User can immediately access the system again
- **Validation**: Ensures user exists and is actually blocked

---

### 3. Get Blocked Users
**GET** `/admin/users/blocked`

Retrieves a paginated list of all blocked users.

#### Request Parameters
None

#### Response
**Success (200)**
```json
{
    "success": true,
    "message": "Blocked users retrieved successfully",
    "data": {
        "blocked_users": [
            {
                "id": 1,
                "user_id": 123,
                "staff_id": 1,
                "reason": "Violation of terms of service",
                "created_at": "2025-07-14T12:30:00.000000Z",
                "updated_at": "2025-07-14T12:30:00.000000Z",
                "user": {
                    "id": 123,
                    "name": "John Doe",
                    "tele_id": "*********",
                    "username": "johndoe",
                    "created_at": "2025-07-14T10:00:00.000000Z"
                },
                "staff": {
                    "id": 1,
                    "name": "Admin User",
                    "email": "<EMAIL>"
                }
            }
        ],
        "pagination": {
            "current_page": 1,
            "total_pages": 1,
            "total_items": 1,
            "per_page": 20
        }
    }
}
```

#### Features
- **Paginated Results**: 20 items per page
- **Relationship Data**: Includes user and staff information
- **Ordered Results**: Newest blocks first
- **Complete Information**: Shows all block details

---

## User Authentication Middleware Protection

### Blocked User Response
When a blocked user attempts to access any authenticated endpoint, they receive:

```json
{
    "success": false,
    "message": "Your account has been blocked",
    "error_code": "USER_BLOCKED",
    "data": {
        "reason": "Violation of terms of service",
        "blocked_at": "2025-07-14T12:30:00.000000Z",
        "contact_support": "Please contact support for assistance"
    }
}
```

### Other Authentication Errors

**Missing Token (401)**
```json
{
    "success": false,
    "message": "Authentication required",
    "error_code": "MISSING_TOKEN"
}
```

**Invalid Token (401)**
```json
{
    "success": false,
    "message": "Invalid or expired token",
    "error_code": "INVALID_TOKEN"
}
```

**Expired Token (401)**
```json
{
    "success": false,
    "message": "Token has expired",
    "error_code": "TOKEN_EXPIRED"
}
```

**Invalid User (401)**
```json
{
    "success": false,
    "message": "Invalid user token",
    "error_code": "INVALID_USER"
}
```

---

## Error Codes Reference

### Client-Side Error Handling
Use these error codes for client-side logic:

| Error Code | Description | HTTP Status | Action |
|------------|-------------|-------------|---------|
| `USER_BLOCKED` | User account is blocked | 403 | Show block message, redirect to support |
| `USER_ALREADY_BLOCKED` | User is already blocked | 409 | Show current block info |
| `USER_NOT_BLOCKED` | User is not blocked | 404 | Show user is not blocked |
| `USER_NOT_FOUND` | User doesn't exist | 404 | Show user not found |
| `MISSING_TOKEN` | No authentication token | 401 | Redirect to login |
| `INVALID_TOKEN` | Invalid authentication token | 401 | Redirect to login |
| `TOKEN_EXPIRED` | Authentication token expired | 401 | Refresh token or login |
| `INVALID_USER` | Invalid user associated with token | 401 | Redirect to login |

### Example Client-Side Handling
```javascript
// Handle API responses
function handleApiResponse(response) {
    if (!response.success) {
        switch (response.error_code) {
            case 'USER_BLOCKED':
                showBlockedMessage(response.data);
                redirectToSupport();
                break;
            case 'MISSING_TOKEN':
            case 'INVALID_TOKEN':
            case 'TOKEN_EXPIRED':
            case 'INVALID_USER':
                redirectToLogin();
                break;
            default:
                showGenericError(response.message);
        }
    }
}

function showBlockedMessage(data) {
    alert(`Your account has been blocked.\nReason: ${data.reason}\nBlocked at: ${data.blocked_at}\n\n${data.contact_support}`);
}
```

---

## Data Models

### Block Record Object
```json
{
    "id": 1,
    "user_id": 123,
    "staff_id": 1,
    "reason": "Violation of terms of service",
    "created_at": "2025-07-14T12:30:00.000000Z",
    "updated_at": "2025-07-14T12:30:00.000000Z",
    "user": {
        "id": 123,
        "name": "John Doe",
        "tele_id": "*********",
        "username": "johndoe"
    },
    "staff": {
        "id": 1,
        "name": "Admin User",
        "email": "<EMAIL>"
    }
}
```

### User Object (in responses)
```json
{
    "id": 123,
    "name": "John Doe",
    "tele_id": "*********",
    "username": "johndoe"
}
```

---

## Security Features

### Immediate Token Revocation
- All user tokens are revoked when blocked
- User cannot access any authenticated endpoints immediately
- Prevents continued access with existing tokens

### Middleware Protection
- `UserAuth` middleware checks block status on every request
- Automatic rejection of blocked users
- Detailed error messages for client handling

### Audit Trail
- Records who blocked/unblocked the user
- Timestamps for all actions
- Preserves historical block information

### Validation
- Ensures users exist before blocking/unblocking
- Prevents duplicate blocks
- Validates input data

---

## Usage Examples

### Block a user
```bash
curl -X POST "http://localhost:8000/api/admin/users/block" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -d '{
    "user_id": 123,
    "reason": "Violation of terms of service"
  }'
```

### Unblock a user
```bash
curl -X POST "http://localhost:8000/api/admin/users/unblock" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -d '{
    "user_id": 123
  }'
```

### Get blocked users list
```bash
curl -X GET "http://localhost:8000/api/admin/users/blocked" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

### Test blocked user access
```bash
curl -X GET "http://localhost:8000/api/user/leaderboard/speed" \
  -H "Authorization: Bearer BLOCKED_USER_TOKEN"
```

---

## Implementation Details

### Database Schema
The blocking system uses the `blocked` table:
- `id` - Primary key
- `user_id` - Foreign key to users table
- `staff_id` - Foreign key to staff table
- `reason` - Text reason for blocking
- `created_at` - Block timestamp
- `updated_at` - Last update timestamp

### Middleware Flow
1. Check for Bearer token
2. Validate token with Laravel Sanctum
3. Check token expiration
4. Get user from token
5. Check if user is blocked
6. If blocked, return error with details
7. If not blocked, continue to endpoint

### Performance Considerations
- Blocking check is performed on every authenticated request
- Database query for block status is optimized
- Consider caching block status for high-traffic applications

---

## Best Practices

### For Administrators
1. **Clear Reasons**: Always provide clear, specific reasons for blocking
2. **Documentation**: Keep records of why users were blocked
3. **Review Process**: Implement a review process for blocks
4. **Communication**: Consider notifying users about blocks through other channels

### For Developers
1. **Error Handling**: Implement proper error handling for all error codes
2. **User Experience**: Provide helpful messages to blocked users
3. **Logging**: Log block/unblock actions for audit purposes
4. **Testing**: Test blocking scenarios in development

### For Client Applications
1. **Graceful Degradation**: Handle blocking gracefully in the UI
2. **User Feedback**: Provide clear feedback about blocked status
3. **Support Links**: Include links to support/appeal processes
4. **Token Management**: Handle token revocation properly