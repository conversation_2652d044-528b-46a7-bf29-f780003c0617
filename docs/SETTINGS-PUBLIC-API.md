# Public Settings API Documentation

## Overview

The Public Settings API provides access to application settings that are safe to expose to client applications. This includes configuration values needed for the client app to function properly without exposing sensitive information.

## Endpoints

### Get Client Settings

- **URL**: `/api/public/client-settings`
- **Method**: `GET`
- **Authentication**: Not required (public endpoint)
- **Description**: Returns settings necessary for client app functionality

#### Response

**Success Response (200 OK):**
```json
{
    "success": true,
    "data": {
        "convert_rate": 2.06,
        "min_withdrawal_amount": "10.00000000",
        "withdrawal_fee": "0.50000000",
        "minimum_claim_ton_amount": 0.1,
        "minimum_withdrawal_ton_amount": 0.35,
        "minimum_withdrawal_shiba_amount": 700000,
        "ton_withdrawal_fee_percent": 0.05,
        "minimum_boot_amount": 0.3
    }
}
```

**Error Response (500 Internal Server Error):**
```json
{
    "success": false,
    "message": "Failed to retrieve client settings",
    "error": "Database connection failed"
}
```

#### Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `convert_rate` | float | Ton mining rate conversion factor |
| `min_withdrawal_amount` | string | Minimum amount required for withdrawal |
| `withdrawal_fee` | string | Fee charged for withdrawals |
| `minimum_claim_ton_amount` | float | Minimum TON amount required for claiming |
| `minimum_withdrawal_ton_amount` | float | Minimum TON amount required for withdrawal |
| `minimum_withdrawal_shiba_amount` | integer | Minimum SHIBA amount required for withdrawal |
| `ton_withdrawal_fee_percent` | float | TON withdrawal fee percentage (0.05 = 5%) |
| `minimum_boot_amount` | float | Minimum boot amount required for certain operations |

#### Field Details

**convert_rate**:
- Ton mining rate conversion factor
- Used to calculate mining rewards and conversions
- Value of 2.06 means 1 unit converts to 2.06 TON

**min_withdrawal_amount**:
- Minimum token balance required to request withdrawal
- Displayed as decimal string for precision

**withdrawal_fee**:
- Fee deducted from withdrawal amount
- Displayed as decimal string for precision

**minimum_claim_ton_amount**:
- Minimum TON amount required to claim rewards
- Used for client-side validation before claiming

**minimum_withdrawal_ton_amount**:
- Minimum TON amount required to initiate withdrawal
- Used for client-side validation before withdrawal request

**minimum_withdrawal_shiba_amount**:
- Minimum SHIBA token amount required for withdrawal
- Used for multi-currency withdrawal validation

**ton_withdrawal_fee_percent**:
- Percentage fee charged for TON withdrawals
- Value of 0.05 means 5% fee will be deducted

**minimum_boot_amount**:
- Minimum boot amount required for certain operations
- Used for client-side validation before boot actions

## Usage Examples

### cURL Example
```bash
curl -X GET "http://your-domain.com/api/public/client-settings" \
     -H "Content-Type: application/json"
```

## Error Handling

The API uses standard HTTP status codes:

- **200 OK**: Settings retrieved successfully
- **500 Internal Server Error**: Server error occurred

Always check the `success` field in the response to determine if the request was successful.

## Notes

- **Public Access**: This endpoint doesn't require authentication
- **Caching**: Consider caching these settings in your client app as they change infrequently
- **Null Values**: Settings may return `null` if not configured in the database
- **Precision**: Decimal values are returned as strings to maintain precision
- **Real-time Updates**: Settings may change during app updates or maintenance

## Security Considerations

- This endpoint only exposes non-sensitive configuration values
- No authentication tokens, database credentials, or admin settings are included
- Safe to call from public client applications

## Integration Guidelines

1. **App Initialization**: Call this endpoint when the app starts
3. **Withdrawal Validation**: Use withdrawal settings for client-side validation:
   - `min_withdrawal_amount`: General withdrawal minimum
   - `minimum_withdrawal_ton_amount`: TON-specific minimum
   - `minimum_withdrawal_shiba_amount`: SHIBA-specific minimum
   - `ton_withdrawal_fee_percent`: TON withdrawal fee calculation
4. **Claim Validation**: Use `minimum_claim_ton_amount` for claim button validation
6. **Fee Calculation**: Use `ton_withdrawal_fee_percent` to calculate withdrawal fees
7. **Error Handling**: Always handle potential failures gracefully
