# User Management API

This document outlines the API endpoints for managing users.

---

## Get User List

Retrieves a paginated list of all users in the system. This endpoint is intended for administrators.

- **Endpoint:** `GET /api/admin/users`
- **Method:** `GET`
- **Authentication:** Requires admin authentication token (`admin.auth` middleware).

### Query Parameters

| Parameter | Type   | Description                                   |
| --------- | ------ | --------------------------------------------- |
| `q`       | string | (Optional) Search for users by their name (partial match). |

### Headers

| Header          | Value                 |
| --------------- | --------------------- |
| `Authorization` | `Bearer {admin_token}` |
| `Accept`        | `application/json`    |

### Success Response (200 OK)

Returns a JSON object containing a paginated list of user objects.

```json
{
    "success": true,
    "message": "Users list retrieved successfully",
    "data": {
        "current_page": 1,
        "data": [
            {
                "id": 1,
                "tele_id": "123456789",
                "name": "<PERSON> Doe",
                "country": "US",
                "language_code": "en",
                "avatar_url": null,
                "last_active": "2023-10-27T10:00:00.000000Z",
                "balance_ton": "0.00000000",
                "balance_token": "0.00000000",
                "created_at": "2023-10-27T10:00:00.000000Z",
                "updated_at": "2023-10-27T10:00:00.000000Z"
            }
        ],
        "first_page_url": "http://localhost/api/admin/users?page=1",
        "from": 1,
        "last_page": 1,
        "last_page_url": "http://localhost/api/admin/users?page=1",
        "links": [
            {
                "url": null,
                "label": "&laquo; Previous",
                "active": false
            },
            {
                "url": "http://localhost/api/admin/users?page=1",
                "label": "1",
                "active": true
            },
            {
                "url": null,
                "label": "Next &raquo;",
                "active": false
            }
        ],
        "next_page_url": null,
        "path": "http://localhost/api/admin/users",
        "per_page": 25,
        "prev_page_url": null,
        "to": 1,
        "total": 1
    }
}
```

### Error Responses

- **401 Unauthorized:** If the admin token is missing or invalid.

```json
{
    "message": "Unauthenticated."
}
```
