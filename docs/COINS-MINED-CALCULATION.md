# Coins Mined Calculation Documentation

## Overview

This document explains the calculation logic for determining how many coins a user has mined based on their mining speed and elapsed time.

## Core Formula

The basic formula for calculating coins mined is:

```
coinsMined = (userSpeed * secondsElapsed) / GAS_PER_SECOND
```

Where:
- `userSpeed`: The user's current mining speed (coins per second in gas units)
- `secondsElapsed`: Time elapsed since the last checkpoint in seconds
- `GAS_PER_SECOND`: Constant value of 1,000,000 (used for precision)

## Constants

### GAS_PER_SECOND
- **Value**: 1,000,000
- **Purpose**: Used as a precision multiplier for mining calculations
- **Location**: `BoostService::GAS_PER_SECOND`

## User Speed Calculation

User speed is determined by:

1. **Base Speed**: Default mining speed for all users
   - Retrieved from `settings` table with key `base_speed`
   - Applied when user has no active boost

2. **Boosted Speed**: Enhanced mining speed from TON payments
   - Calculated using: `(convertRate * tonAmount * GAS_PER_SECOND) / (30 * 24 * 60 * 60)`
   - Where `convertRate` is retrieved from `settings` table
   - Applied for 30 days after TON payment

## Time Calculation

Time elapsed is calculated as:
```php
$secondsElapsed = $lastCheckpoint->diffInSeconds($now);
```

Where:
- `$lastCheckpoint`: User's last mining checkpoint timestamp
- `$now`: Current timestamp or specified end time

## Implementation Details

### BoostService::calculatePendingTokens()

This method calculates pending tokens without updating the database:

```php
public function calculatePendingTokens(User $user, $endTime = null): float
{
    $now = $endTime ?? Carbon::now();
    $lastCheckpoint = $user->last_checkpoint ?? $now;
    
    $secondsElapsed = $lastCheckpoint->diffInSeconds($now);
    
    if ($secondsElapsed <= 0) {
        return 0;
    }
    
    $coinsMined = $user->speed * $secondsElapsed / self::GAS_PER_SECOND;
    
    return $coinsMined;
}
```

### Key Features

1. **Flexible End Time**: Can calculate for any time period by specifying `$endTime`
2. **Safety Check**: Returns 0 if no time has elapsed
3. **Precision**: Uses integer arithmetic with GAS_PER_SECOND for precision
4. **No Side Effects**: Pure calculation function that doesn't modify database

## Usage Examples

### Example 1: Regular Mining
```php
$user = User::find(1);
$user->speed = 5000000; // 5 coins per second in gas units
$user->last_checkpoint = Carbon::now()->subHours(1); // 1 hour ago

$coinsMined = $boostService->calculatePendingTokens($user);
// Result: 5000000 * 3600 / 1000000 = 18 coins
```

### Example 2: Boost Expiration
```php
$user = User::find(1);
$user->speed = 10000000; // 10 coins per second (boosted)
$user->last_checkpoint = Carbon::now()->subHours(2);
$user->boost_expired_at = Carbon::now()->subMinutes(30);

// Calculate only until boost expiration
$coinsMined = $boostService->calculatePendingTokens($user, $user->boost_expired_at);
// Result: 10000000 * 5400 / 1000000 = 54 coins (1.5 hours at boosted speed)
```

## Database Updates

The calculation function only returns the amount. Database updates are handled separately by:

1. **saveCheckpoint()**: Updates `pending_token` and `last_checkpoint`
2. **processMining()**: Updates `pending_token` and `last_checkpoint` after calculation
3. **processBoostExpiration()**: Updates user speed and clears boost expiration

## Error Handling

- Returns `0` if `secondsElapsed <= 0`
- Handles null `last_checkpoint` by using current time
- No division by zero risk (GAS_PER_SECOND is constant)

## Performance Considerations

- Lightweight calculation with minimal database queries
- Uses Carbon for efficient time calculations
- No complex floating-point operations (uses integer arithmetic)
- Separate calculation and update functions for flexibility

## Integration Points

This calculation is used by:
- **Mining API**: Real-time mining progress updates
- **Boost System**: When applying/expiring boosts
- **Scheduled Tasks**: Batch processing of user mining
- **Admin Tools**: User balance calculations

## Testing

Key test scenarios:
1. Zero elapsed time
2. Regular mining intervals
3. Boost expiration timing
4. Edge cases (null checkpoints, future times)