# Admin Settings API Documentation

## Overview

The Admin Settings API provides access to all application settings for administrative purposes. This includes both public settings and sensitive configuration values that should only be accessible to administrators.

## Authentication

All admin endpoints require admin authentication via the `admin.auth` middleware. Ensure you have proper admin credentials before accessing these endpoints.

## Endpoints

### Get Admin Settings

- **URL**: `/api/admin/admin-settings`
- **Method**: `GET`
- **Authentication**: Required (admin.auth middleware)
- **Description**: Returns all settings needed for admin panel functionality

#### Response

**Success Response (200 OK):**
```json
{
    "success": true,
    "data": {
        "base_speed": 1000000,
        "convert_rate": 100.5,
        "app_name": "Mining App",
        "app_version": "1.0.0",
        "maintenance_mode": false,
        "min_withdrawal_amount": "10.00000000",
        "withdrawal_fee": "0.50000000",
        "max_boost_duration": 30,
        "admin_email": "<EMAIL>",
        "smtp_host": "smtp.example.com",
        "smtp_port": 587,
        "telegram_bot_token": "1234567890:ABCdefGHIjklMNOpqrSTUvwxyz",
        "ton_wallet_address": "EQD...",
        "debug_mode": false,
        "minimum_claim_ton_amount": "1.00000000"
    }
}
```

**Error Response (500 Internal Server Error):**
```json
{
    "success": false,
    "message": "Failed to retrieve admin settings",
    "error": "Database connection failed"
}
```

#### Response Fields

| Field | Type | Description | Category |
|-------|------|-------------|----------|
| `base_speed` | integer | Base mining speed in gas units per second | Mining |
| `convert_rate` | float | TON to token conversion rate | Mining |
| `app_name` | string | Application name | General |
| `app_version` | string | Current application version | General |
| `maintenance_mode` | boolean | Whether app is in maintenance mode | General |
| `min_withdrawal_amount` | string | Minimum amount required for withdrawal | Financial |
| `withdrawal_fee` | string | Fee charged for withdrawals | Financial |
| `max_boost_duration` | integer | Maximum boost duration in days | Mining |
| `admin_email` | string | Administrator email address | Admin |
| `smtp_host` | string | SMTP server hostname | Email |
| `smtp_port` | integer | SMTP server port | Email |
| `telegram_bot_token` | string | Telegram bot authentication token | Integration |
| `ton_wallet_address` | string | TON wallet address for payments | Blockchain |
| `debug_mode` | boolean | Whether debug mode is enabled | Development |
| `minimum_claim_ton_amount` | string | Minimum TON amount for claiming | Financial |

#### Field Details

**Mining Settings:**
- `base_speed`: Default mining speed for users without boosts
- `convert_rate`: Multiplier for TON to mining speed conversion
- `max_boost_duration`: Maximum days a boost can last

**Financial Settings:**
- `min_withdrawal_amount`: Minimum tokens required for withdrawal
- `withdrawal_fee`: Fee deducted from withdrawals
- `minimum_claim_ton_amount`: Minimum TON required for claiming rewards

**Integration Settings:**
- `telegram_bot_token`: Token for Telegram bot integration
- `ton_wallet_address`: Wallet address for receiving TON payments

**System Settings:**
- `maintenance_mode`: Controls app availability
- `debug_mode`: Enables additional logging and debugging features

**Communication Settings:**
- `admin_email`: Contact email for admin notifications
- `smtp_host`, `smtp_port`: Email server configuration

## Usage Examples

### cURL Example
```bash
curl -X GET "http://your-domain.com/api/admin/admin-settings" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

### JavaScript Example
```javascript
const response = await fetch('/api/admin/admin-settings', {
    method: 'GET',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${adminToken}`
    }
});

const data = await response.json();

if (data.success) {
    const settings = data.data;
    
    // Update admin panel with settings
    updateMiningSettings({
        baseSpeed: settings.base_speed,
        convertRate: settings.convert_rate,
        maxBoostDuration: settings.max_boost_duration
    });
    
    updateFinancialSettings({
        minWithdrawal: settings.min_withdrawal_amount,
        withdrawalFee: settings.withdrawal_fee,
        minClaimAmount: settings.minimum_claim_ton_amount
    });
    
    updateSystemSettings({
        maintenanceMode: settings.maintenance_mode,
        debugMode: settings.debug_mode
    });
    
} else {
    console.error('Failed to load admin settings:', data.message);
    showErrorMessage(data.message);
}
```

### React Admin Panel Example
```javascript
import { useEffect, useState } from 'react';

function useAdminSettings(adminToken) {
    const [settings, setSettings] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        if (!adminToken) return;

        fetch('/api/admin/admin-settings', {
            headers: {
                'Authorization': `Bearer ${adminToken}`
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                setSettings(data.data);
            } else {
                setError(data.message);
            }
        })
        .catch(err => setError(err.message))
        .finally(() => setLoading(false));
    }, [adminToken]);

    return { settings, loading, error };
}

// Usage in admin component
function AdminSettingsPanel({ adminToken }) {
    const { settings, loading, error } = useAdminSettings(adminToken);

    if (loading) return <div>Loading admin settings...</div>;
    if (error) return <div>Error: {error}</div>;

    return (
        <div className="admin-settings">
            <h2>System Settings</h2>
            
            <div className="mining-settings">
                <h3>Mining Configuration</h3>
                <p>Base Speed: {settings.base_speed}</p>
                <p>Convert Rate: {settings.convert_rate}</p>
                <p>Max Boost Duration: {settings.max_boost_duration} days</p>
            </div>
            
            <div className="financial-settings">
                <h3>Financial Configuration</h3>
                <p>Min Withdrawal: {settings.min_withdrawal_amount}</p>
                <p>Withdrawal Fee: {settings.withdrawal_fee}</p>
                <p>Min Claim Amount: {settings.minimum_claim_ton_amount}</p>
            </div>
            
            <div className="integration-settings">
                <h3>Integration Settings</h3>
                <p>Telegram Bot: {settings.telegram_bot_token ? 'Configured' : 'Not Set'}</p>
                <p>TON Wallet: {settings.ton_wallet_address}</p>
            </div>
        </div>
    );
}
```

## Error Handling

The API uses standard HTTP status codes:

- **200 OK**: Settings retrieved successfully
- **401 Unauthorized**: Invalid or missing admin authentication
- **403 Forbidden**: User doesn't have admin privileges
- **500 Internal Server Error**: Server error occurred

Always check the `success` field in the response to determine if the request was successful.

## Security Considerations

**⚠️ IMPORTANT SECURITY NOTES:**

1. **Sensitive Data**: This endpoint returns sensitive information including:
   - Telegram bot tokens
   - SMTP credentials
   - Admin email addresses
   - TON wallet addresses

2. **Admin Only**: This endpoint is protected by admin authentication middleware
3. **Token Protection**: Never expose admin tokens in client-side code
4. **HTTPS Only**: Always use HTTPS when calling this endpoint
5. **Access Logging**: Consider logging all admin settings access for security auditing

## Integration Guidelines

1. **Admin Panel**: Use for populating admin configuration panels
2. **System Monitoring**: Monitor critical settings like maintenance mode
3. **Configuration Management**: Allow admins to view current system configuration
4. **Backup/Restore**: Use for backing up system configuration
5. **Troubleshooting**: Access debug settings for system diagnostics

## Available Settings by Category

### Mining & Performance
- `base_speed`: Base mining speed
- `convert_rate`: TON conversion rate
- `max_boost_duration`: Maximum boost duration

### Financial & Transactions
- `min_withdrawal_amount`: Minimum withdrawal amount
- `withdrawal_fee`: Withdrawal fee
- `minimum_claim_ton_amount`: Minimum claim amount

### System & Maintenance
- `maintenance_mode`: Maintenance status
- `debug_mode`: Debug status
- `app_name`: Application name
- `app_version`: Application version

### Communications
- `admin_email`: Administrator email
- `smtp_host`: SMTP server
- `smtp_port`: SMTP port

### External Integrations
- `telegram_bot_token`: Telegram bot token
- `ton_wallet_address`: TON wallet address

## Notes

- **Null Values**: Settings may return `null` if not configured in the database
- **Precision**: Decimal values are returned as strings to maintain precision
- **Real-time**: Settings reflect current database values
- **Caching**: Consider caching with short TTL due to sensitive nature
- **Audit Trail**: All admin settings access should be logged for security