# User Sync API Documentation

## Overview
The User Sync API provides a single endpoint to handle both user initialization and restoration scenarios based on Telegram user data.

## Endpoint
- **URL**: `/api/users/sync`
- **Method**: `POST`
- **Authentication**: Not required (public endpoint)

## Functionality
The API automatically determines whether to:
1. **INIT User**: Create a new user if they don't exist
2. **Restored User**: Update existing user's last activity if they exist

## Request Payload

### Required Fields
- `telegram_id` (string): The user's Telegram ID (mapped to `tele_id` in database)
- `name` (string, max 255): The user's name

### Optional Fields
- `country` (string, max 255): The user's country
- `language_code` (string, max 10): The user's language code
- `username` (string, max 255): The user's username
- `avatar_url` (string, max 255): URL to the user's avatar image
- `referral_code` (string, max 8): Referral code if user was referred
- `device_name` (string): Device identifier (defaults to 'mobile-app')

### Example Request
```json
{
    "telegram_id": "123456789",
    "name": "<PERSON> Doe",
    "country": "US",
    "language_code": "en",
    "username": "johndoe",
    "avatar_url": "https://example.com/avatar.jpg",
    "referral_code": "ABC123",
    "device_name": "ios-app"
}
```

## Response Format

### Success Response Structure
```json
{
    "success": true,
    "message": "User initialized successfully" | "User restored successfully",
    "data": {
        "user": {
            "id": 1,
            "tele_id": "123456789",
            "name": "John Doe",
            "username": "johndoe",
            "country": "US",
            "language_code": "en",
            "avatar_url": "https://example.com/avatar.jpg",
            "last_active": "2025-06-10T17:55:30.000000Z",
            "balance_ton": "0.00000000",
            "balance_token": "0.00000000",
            "speed": 1000000,
            "pending_token": "5.50000000",
            "last_checkpoint": "2025-06-10T16:30:00.000000Z",
            "created_at": "2025-06-10T17:55:30.000000Z",
            "updated_at": "2025-06-10T17:55:30.000000Z",
            "referral_code": "XYZ789"
        },
        "token": "2|laravel_sanctum_token_string...",
        "token_expires_at": "2025-07-10T17:55:30.000000Z",
        "action": "initialized" | "restored",
        "referral": {
            "referred_by": {
                "id": 2,
                "name": "Jane Smith",
                "username": "janesmith"
            },
            "referral_created": true
        }
    }
}
```

### Response Fields Description

**User Object Fields:**
- `id`: User's unique identifier
- `tele_id`: Telegram user ID
- `name`: User's display name
- `username`: Telegram username
- `country`: User's country code
- `language_code`: User's language preference
- `avatar_url`: URL to user's avatar image
- `last_active`: Timestamp of last activity
- `balance_ton`: User's TON cryptocurrency balance
- `balance_token`: User's token balance
- `speed`: Current mining speed (in gas units per second)
- `pending_token`: Real-time calculated pending tokens (includes mined coins since last checkpoint)
- `last_checkpoint`: Timestamp of last mining checkpoint
- `created_at`: User creation timestamp
- `updated_at`: User last update timestamp
- `referral_code`: User's unique referral code

**Additional Response Fields:**
- `token`: Sanctum authentication token
- `token_expires_at`: Token expiration timestamp
- `action`: Either "initialized" (new user) or "restored" (existing user)
- `referral`: Only included when a new user is created with a valid referral code

### Mining-Related Fields

The UserResource automatically processes mining calculations:
- **`pending_token`**: Includes both stored pending tokens and real-time calculated coins mined since last checkpoint
- **`speed`**: Current mining speed, which may be boosted or base speed
- **Boost Processing**: Automatically checks and processes boost expiration before returning data

Note: The `referral` object is only included when a new user is created with a valid referral code.

### Error Response Structure
```json
{
    "success": false,
    "message": "Validation failed",
    "errors": {
        "telegram_id": ["The telegram id field is required."],
        "name": ["The name field is required."]
    }
}
```

## Response Status Codes

- **201 Created**: User was successfully initialized (new user)
- **200 OK**: User was successfully restored (existing user)
- **422 Unprocessable Entity**: Validation failed

## Behavior Details

### When User Exists (Restored Scenario)
- Updates `username` and `avatar_url` if provided
- Updates `last_active` timestamp to current time
- Revokes previous tokens for this device and creates a new one
- Processes boost expiration if applicable
- Calculates real-time pending tokens without updating database
- Returns status 200 with action "restored"

### When User Doesn't Exist (Init Scenario)
- Creates new user with provided data
- Sets default balances (balance_ton: 0, balance_token: 0)
- Sets default mining speed based on system settings
- Generates a unique referral code for the user
- Processes referral relationship if a valid referral code is provided
- Creates a new authentication token
- Returns status 201 with action "initialized"

### Real-Time Mining Calculations
The UserResource performs real-time mining calculations for all users:
- **Boost Expiration**: Automatically checks and processes boost expiration
- **Pending Tokens**: Calculates coins mined since last checkpoint and adds to stored pending tokens
- **No Database Updates**: Mining calculations are performed for display only and don't update the database
- **Performance**: Uses efficient calculation methods to minimize response time

## Authentication Token

The response includes a Sanctum authentication token that:
- Is valid for 30 days
- Should be included in subsequent authenticated requests
- Is specific to the device that made the request

## Example Usage

### cURL Example
```bash
curl -X POST "http://your-domain.com/api/users/sync" \
     -H "Content-Type: application/json" \
     -d '{
         "telegram_id": "123456789",
         "name": "John Doe",
         "country": "US",
         "language_code": "en",
         "username": "johndoe",
         "device_name": "curl-test"
     }'
```

### JavaScript Example
```javascript
const response = await fetch('/api/users/sync', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        telegram_id: '123456789',
        name: 'John Doe',
        country: 'US',
        language_code: 'en',
        username: 'johndoe',
        device_name: 'web-app'
    })
});

const data = await response.json();
console.log(data);

// Store the token for future authenticated requests
if (data.success) {
    localStorage.setItem('auth_token', data.data.token);
}
```

## Validation Rules

| Field | Rules |
|-------|--------|
| telegram_id | required, string |
| name | required, string, max:255 |
| country | nullable, string, max:255 |
| language_code | nullable, string, max:10 |
| username | nullable, string, max:255 |
| avatar_url | nullable, string, max:255 |
| referral_code | nullable, string, max:8 |

## Notes

- The endpoint uses `telegram_id` in the request payload but maps it to `tele_id` in the database
- Users are uniquely identified by their `tele_id` field
- Default cryptocurrency balances are set to 0 for new users
- The `last_active` field is always updated to the current timestamp
- For existing users, only `username`, `avatar_url`, and `last_active` are updated
- Authentication tokens expire after 30 days
- **Mining Fields**: The response includes real-time mining data:
  - `speed`: Current mining speed (may be boosted or base speed)
  - `pending_token`: Real-time calculated total including mined coins since last checkpoint
  - `last_checkpoint`: Timestamp of last mining checkpoint update
- **Boost Processing**: UserResource automatically processes boost expiration before returning data
- **Performance**: Mining calculations use efficient methods and don't update the database during API calls
