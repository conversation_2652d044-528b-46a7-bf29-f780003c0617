# Staff Authentication API Guide

This guide explains how to use the staff authentication API endpoints.

## Base URL

All API endpoints are prefixed with `/api`.

## Authentication

Protected routes require an API token. To authenticate, include the token in the `Authorization` header of your request as a Bearer token.

`Authorization: Bearer <YOUR_API_TOKEN>`

You can obtain this token from the login endpoint.

---

## Login

Logs in a staff member and returns an API token.

-   **Endpoint:** `/staff/auth/login`
-   **Method:** `POST`
-   **Authentication:** None required.

### Request Body

| Field      | Type   | Description              |
| :--------- | :----- | :----------------------- |
| `username` | string | **Required.** The staff member's username. |
| `password` | string | **Required.** The staff member's password. |

### Example Request (cURL)

```bash
curl -X POST \
  http://<your-domain>/api/staff/auth/login \
  -H 'Content-Type: application/json' \
  -d '{
    "username": "staff_user",
    "password": "password123"
}'
```

### Success Response (200 OK)

```json
{
    "success": true,
    "message": "Login successful",
    "data": {
        "staff": {
            "id": 1,
            "username": "staff_user",
            "created_at": "2023-10-27T10:00:00.000000Z"
        },
        "token": "1|abcdefghijklmnopqrstuvwxyz123456"
    }
}
```

### Error Responses

-   **422 Unprocessable Entity** (Validation failed)
    ```json
    {
        "success": false,
        "message": "Validation failed",
        "errors": {
            "username": [
                "The username field is required."
            ]
        }
    }
    ```
-   **401 Unauthorized** (Invalid credentials)
    ```json
    {
        "success": false,
        "message": "Invalid credentials"
    }
    ```

---

## Logout

Logs out the currently authenticated staff member by invalidating their token.

-   **Endpoint:** `/staff/auth/logout`
-   **Method:** `POST`
-   **Authentication:** Bearer Token required.

### Example Request (cURL)

```bash
curl -X POST \
  http://<your-domain>/api/staff/auth/logout \
  -H 'Authorization: Bearer <YOUR_API_TOKEN>'
```

### Success Response (200 OK)

```json
{
    "success": true,
    "message": "Logged out successfully"
}
```
