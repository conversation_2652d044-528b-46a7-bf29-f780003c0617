# BoostService Documentation

## Overview

The `BoostService` class is a core service that manages mining speed boosts for users in the cryptocurrency mining application. It handles TON-based speed upgrades, mining calculations, checkpoint management, and boost expiration processing.

## Class Location
- **Path**: `app/Services/BoostService.php`
- **Namespace**: `App\Services`

## Dependencies

### Required Models
- `App\Models\User` - User management
- `App\Models\Setting` - Configuration settings

### Required Packages
- `Carbon\Carbon` - Date/time handling
- `Illuminate\Support\Facades\DB` - Database transactions

## Class Constants

### `GAS_PER_SECOND`
- **Value**: `1000000`
- **Purpose**: Conversion factor for calculating mining rates per second
- **Usage**: Used in mining calculations to convert speed units to token amounts

## Public Methods

### `applyBoost(User $user, float $tonAmount): bool`

Applies a speed boost to a user based on TON amount invested.

**Parameters:**
- `$user` (User) - The user receiving the boost
- `$tonAmount` (float) - Amount of TON cryptocurrency to convert to speed

**Returns:**
- `bool` - `true` on success, `false` on failure

**Process:**
1. Saves current mining checkpoint using `saveCheckpoint()`
2. Calculates new speed from TON amount using `calculateSpeedFromTon()`
3. Sets boost expiration to 30 days from current time
4. Updates user's speed and expiration date
5. Uses database transactions for data integrity

**Database Transaction:**
- Wraps all operations in `DB::beginTransaction()` and `DB::commit()`
- Rolls back on any exception with `DB::rollback()`

**Example:**
```php
$boostService = new BoostService();
$success = $boostService->applyBoost($user, 5.0); // Apply boost with 5 TON

if ($success) {
    echo "Boost applied successfully!";
} else {
    echo "Failed to apply boost";
}
```

### `processBoostExpiration(User $user): bool`

Processes boost expiration for a user, resetting speed to base level when boost expires.

**Parameters:**
- `$user` (User) - The user to check for boost expiration

**Returns:**
- `bool` - `true` if boost was expired and processed, `false` if no action taken

**Process:**
1. Checks if `boost_expired_at` exists and is in the past
2. Calculates time difference between expiration and last checkpoint
3. Saves checkpoint up to expiration time if needed
4. Resets speed to base level using `getBaseSpeed()`
5. Clears boost expiration date

**Example:**
```php
$expired = $boostService->processBoostExpiration($user);

if ($expired) {
    echo "Boost expired and reset to base speed";
} else {
    echo "Boost still active";
}
```

### `processMining(User $user): array`

Processes mining progress for a user, calculating tokens mined since last checkpoint.

**Parameters:**
- `$user` (User) - The user to process mining for

**Returns:**
- `array` - Result array with success status and mining details

**Return Format:**
```php
// Success case
[
    'success' => true,
    'coins_mined' => 150.5
]

// No progress case
[
    'success' => false,
    'message' => 'No mining progress since last checkpoint'
]
```

**Process:**
1. Calls `processBoostExpiration()` to handle expired boosts
2. Calculates time elapsed since last checkpoint in seconds
3. Calculates tokens mined using speed and time
4. Updates user's pending tokens and checkpoint timestamp

**Example:**
```php
$result = $boostService->processMining($user);

if ($result['success']) {
    echo "Mined: " . $result['coins_mined'] . " tokens";
} else {
    echo $result['message'];
}
```

## Private Methods

### `saveCheckpoint(User $user, $time = null): void`

Saves a mining checkpoint and calculates tokens mined since last checkpoint.

**Parameters:**
- `$user` (User) - The user to save checkpoint for
- `$time` (Carbon|null) - Optional timestamp (defaults to current time)

**Process:**
1. Uses provided time or current time as checkpoint
2. Calculates time elapsed since last checkpoint in seconds
3. Calculates tokens mined using formula: `speed * seconds / GAS_PER_SECOND`
4. Updates user's pending tokens and checkpoint timestamp

**Formula:**
```php
$coinsMined = $user->speed * $secondsElapsed / self::GAS_PER_SECOND;
```

### `calculateSpeedFromTon(float $tonAmount): float`

Converts TON amount to mining speed using configurable conversion rate.

**Parameters:**
- `$tonAmount` (float) - Amount of TON to convert

**Returns:**
- `float` - Calculated mining speed per second

**Formula:**
```php
$convertRate = Setting::get('convert_rate');
$monthProfit = $convertRate * $tonAmount;
$secProfit = ($monthProfit * self::GAS_PER_SECOND) / (30 * 24 * 60 * 60);
```

**Dependencies:**
- Requires `convert_rate` setting to be configured in database

### `getBaseSpeed(): float`

Retrieves the base mining speed for users without boosts.

**Returns:**
- `float` - Base mining speed from settings

**Dependencies:**
- Requires `base_speed` setting to be configured in database

## Required Settings

The service depends on the following settings being configured in the `settings` table:

### `convert_rate`
- **Type**: Numeric
- **Purpose**: Rate for converting TON to monthly profit
- **Usage**: Used in `calculateSpeedFromTon()` method
- **Example**: `10.0` (1 TON = 10 units monthly profit)

### `base_speed`
- **Type**: Numeric  
- **Purpose**: Base mining speed for users without active boosts
- **Usage**: Used in `getBaseSpeed()` method when boost expires
- **Example**: `1000000` (1 token per second at base rate)

## Database Fields

The service works with these User model fields:

### `speed`
- **Type**: `decimal(20,8)`
- **Purpose**: Current mining speed (tokens per second multiplied by GAS_PER_SECOND)
- **Default**: `0`

### `pending_token`
- **Type**: `decimal(20,8)`
- **Purpose**: Tokens mined but not yet claimed by user
- **Default**: `0`

### `last_checkpoint`
- **Type**: `timestamp`
- **Purpose**: Last time mining progress was calculated
- **Default**: `null`

### `boost_expired_at`
- **Type**: `timestamp`
- **Purpose**: When current boost expires (30 days from application)
- **Default**: `null`

## API Integration

The service is used by the `BoostController` which provides these endpoints:

### `POST /api/user/boost/apply`
- Applies boost using `applyBoost()` method
- Requires `ton_amount` parameter
- Protected by user authentication

### `POST /api/user/boost/process-mining`
- Processes mining using `processMining()` method
- No parameters required
- Protected by user authentication

## Usage Examples

### Basic Boost Application
```php
use App\Services\BoostService;

$boostService = new BoostService();
$user = User::find(1);

// Apply 10 TON boost
$success = $boostService->applyBoost($user, 10.0);

if ($success) {
    echo "Boost applied successfully!";
    echo "New speed: " . $user->fresh()->speed;
    echo "Expires: " . $user->fresh()->boost_expired_at;
} else {
    echo "Failed to apply boost";
}
```

### Mining Processing
```php
$result = $boostService->processMining($user);

if ($result['success']) {
    echo "Mined: " . $result['coins_mined'] . " tokens";
    echo "Total pending: " . $user->fresh()->pending_token;
} else {
    echo $result['message'];
}
```

### Scheduled Job Integration
```php
// In a Laravel scheduled job
$users = User::whereNotNull('speed')->get();

foreach ($users as $user) {
    $boostService->processMining($user);
}
```

## Error Handling

### Database Transactions
The `applyBoost()` method uses database transactions to ensure data consistency:
- All operations wrapped in transaction
- Automatic rollback on any exception
- Returns `false` on failure

### Exception Handling
- Database exceptions are caught and handled gracefully
- Service methods return boolean or array responses for easy error checking
- No exceptions are thrown to calling code

## Performance Considerations

### Efficient Calculations
- Uses seconds-based calculations for precise mining computations
- Avoids floating-point precision issues with large numbers
- Checkpoint system prevents duplicate mining calculations

### Database Optimization
- Minimal database queries per operation
- Batch updates where possible
- Proper indexing on timestamp fields recommended

### Memory Usage
- Stateless service design
- No data caching within service
- Relies on Laravel's model caching

## Security Considerations

### Input Validation
- TON amounts should be validated before passing to service
- User authentication handled at controller level
- No direct user input processing in service

### Data Integrity
- Database transactions ensure atomicity
- Checkpoint system prevents double-spending of mining time
- Expiration checks prevent indefinite boosts

## Testing

### Unit Tests
Recommended test cases:
- Boost application with various TON amounts
- Mining calculations with different time intervals
- Boost expiration processing
- Edge cases (zero amounts, expired boosts, etc.)

### Integration Tests
- Full boost lifecycle from application to expiration
- Mining processing with expired boosts
- Database consistency after failures

## Configuration

### Environment Setup
Ensure these settings exist in your `settings` table:

```sql
INSERT INTO settings (key, value, type, description) VALUES
('convert_rate', '10.0', 'number', 'TON to monthly profit conversion rate'),
('base_speed', '1000000', 'number', 'Base mining speed for users');
```

### Migration Requirements
The service requires these database fields to exist:
- `users.speed`
- `users.pending_token`
- `users.last_checkpoint`
- `users.boost_expired_at`

## Troubleshooting

### Common Issues

1. **Settings not found**: Ensure `convert_rate` and `base_speed` settings exist
2. **Database rollback**: Check database permissions and connection
3. **Incorrect mining calculations**: Verify GAS_PER_SECOND constant value
4. **Boost not expiring**: Ensure scheduled jobs are running to process expiration

### Debug Information
Enable database query logging to monitor service performance:
```php
DB::enableQueryLog();
// ... service operations ...
$queries = DB::getQueryLog();
```

## Changelog

### Version 1.0
- Initial implementation
- Basic boost application and mining processing
- Checkpoint system implementation
- Boost expiration handling