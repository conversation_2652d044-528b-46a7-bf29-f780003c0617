# Laravel Modules - Quick Command Reference

## Module Management

```bash
# Create a new module
docker-compose exec backend php artisan module:make {ModuleName}

# List all modules with status
docker-compose exec backend php artisan module:list

# Enable a module
docker-compose exec backend php artisan module:enable {ModuleName}

# Disable a module
docker-compose exec backend php artisan module:disable {ModuleName}

# Delete a module
docker-compose exec backend php artisan module:delete {ModuleName}
```

## Code Generation

### Controllers
```bash
# Basic controller
docker-compose exec backend php artisan module:make-controller {ControllerName} {ModuleName}

# API controller (with index, store, show, update, destroy methods)
docker-compose exec backend php artisan module:make-controller {ControllerName} {ModuleName} --api

# Controller with resource methods
docker-compose exec backend php artisan module:make-controller {ControllerName} {ModuleName} --resource
```

### Models
```bash
# Basic model
docker-compose exec backend php artisan module:make-model {ModelName} {ModuleName}

# Model with migration
docker-compose exec backend php artisan module:make-model {ModelName} {ModuleName} --migration

# Model with factory
docker-compose exec backend php artisan module:make-model {ModelName} {ModuleName} --factory
```

### Database
```bash
# Create migration
docker-compose exec backend php artisan module:make-migration {migration_name} {ModuleName}

# Create seeder
docker-compose exec backend php artisan module:make-seeder {SeederName} {ModuleName}

# Create factory
docker-compose exec backend php artisan module:make-factory {FactoryName} {ModuleName}
```

### Requests & Resources
```bash
# Form request
docker-compose exec backend php artisan module:make-request {RequestName} {ModuleName}

# API resource
docker-compose exec backend php artisan module:make-resource {ResourceName} {ModuleName}
```

### Services & Other Classes
```bash
# Service class
docker-compose exec backend php artisan module:make-service {ServiceName} {ModuleName}

# Event class
docker-compose exec backend php artisan module:make-event {EventName} {ModuleName}

# Listener class
docker-compose exec backend php artisan module:make-listener {ListenerName} {ModuleName}

# Job class
docker-compose exec backend php artisan module:make-job {JobName} {ModuleName}

# Notification class
docker-compose exec backend php artisan module:make-notification {NotificationName} {ModuleName}
```

### Testing
```bash
# Feature test
docker-compose exec backend php artisan module:make-test {TestName} {ModuleName} --feature

# Unit test
docker-compose exec backend php artisan module:make-test {TestName} {ModuleName} --unit
```

## Database Operations

```bash
# Run all migrations (including modules)
docker-compose exec backend php artisan migrate

# Run migrations for specific module
docker-compose exec backend php artisan module:migrate {ModuleName}

# Rollback module migrations
docker-compose exec backend php artisan module:migrate-rollback {ModuleName}

# Reset module migrations
docker-compose exec backend php artisan module:migrate-reset {ModuleName}

# Refresh module migrations
docker-compose exec backend php artisan module:migrate-refresh {ModuleName}

# Seed module database
docker-compose exec backend php artisan module:seed {ModuleName}
```

## Publishing & Assets

```bash
# Publish module assets
docker-compose exec backend php artisan module:publish {ModuleName}

# Publish module configuration
docker-compose exec backend php artisan module:publish-config {ModuleName}

# Publish module migrations to main app
docker-compose exec backend php artisan module:publish-migration {ModuleName}
```

## Development Helpers

```bash
# Clear route cache
docker-compose exec backend php artisan route:clear

# Clear all caches
docker-compose exec backend php artisan optimize:clear

# Regenerate autoloader
docker-compose exec backend composer dump-autoload

# List all routes (including module routes)
docker-compose exec backend php artisan route:list

# List routes for specific module
docker-compose exec backend php artisan route:list --path=v1/module-prefix
```

## Example Workflow

### Creating a new "Blog" module:

```bash
# 1. Create the module
docker-compose exec backend php artisan module:make Blog

# 2. Create a Post model with migration
docker-compose exec backend php artisan module:make-model Post Blog --migration

# 3. Create an API controller
docker-compose exec backend php artisan module:make-controller PostController Blog --api

# 4. Create request validation
docker-compose exec backend php artisan module:make-request PostRequest Blog

# 5. Create API resource
docker-compose exec backend php artisan module:make-resource PostResource Blog

# 6. Create service class
docker-compose exec backend php artisan module:make-service PostService Blog

# 7. Run migrations
docker-compose exec backend php artisan migrate

# 8. Create seeder
docker-compose exec backend php artisan module:make-seeder PostSeeder Blog

# 9. Create tests
docker-compose exec backend php artisan module:make-test PostTest Blog --feature
```

## Tips

- Always use the module name as the last parameter in commands
- Module names should be in StudlyCase (e.g., UserManagement, BlogPost)
- Use descriptive names for controllers, models, and other classes
- Follow Laravel naming conventions within modules
- Test your module functionality after creation
