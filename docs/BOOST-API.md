# Boost API Documentation

## Overview
The Boost API allows users to pay TON cryptocurrency to increase their mining speed. The system validates TON payments on the blockchain and applies speed boosts to users' mining operations.

## Endpoint

### POST /api/user/boost

**Description:** Purchase mining speed boost with TON payment

**Authentication:** Required (user.auth middleware)

**Content-Type:** application/json

## Request Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `ton_amount` | numeric | Yes | Amount of TON to pay (minimum: 3) |

## Request Example

```json
{
  "ton_amount": 5.0
}
```

## Validation Process

The system performs the following validation steps:

1. **Input Validation**: Ensures `ton_amount` is numeric and >= 3
2. **Blockchain Validation**: Searches TON blockchain for transaction with:
   - **Message format**: `mining_lite_{{user_id}}`
   - **Amount**: Exact TON amount (converted to nanoTON)
   - **Recipient**: System's TON receiver address
3. **Boost Application**: If validation passes, applies speed boost using BoostService

## Response Format

### Success Response (200)

```json
{
  "success": true,
  "message": "Boost payment completed successfully",
  "data": {
    "transaction_hash": "abc123def456",
    "ton_amount": 5.0,
    "boost_expired_at": "2025-08-13T13:44:28.000000Z",
    "new_speed": 15000000
  }
}
```

### Validation Failed Response (400)

```json
{
  "success": false,
  "message": "Transaction not found on blockchain or failed validation",
  "data": {
    "expected_message": "mining_lite_123",
    "expected_amount": 5000000000,
    "ton_amount": 5.0
  }
}
```

### Processing Error Response (500)

```json
{
  "success": false,
  "message": "Transaction confirmed on blockchain, but failed to apply boost."
}
```

## Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `success` | boolean | Operation success status |
| `message` | string | Human-readable status message |
| `transaction_hash` | string | Generated transaction hash for record |
| `ton_amount` | numeric | Amount of TON processed |
| `boost_expired_at` | datetime | When the boost expires (30 days from application) |
| `new_speed` | numeric | User's new mining speed after boost |
| `expected_message` | string | Expected blockchain message format |
| `expected_amount` | integer | Expected amount in nanoTON |

## Implementation Details

### Message Format
- **Pattern**: `mining_lite_{{user_id}}`
- **Example**: For user ID 123, message should be `mining_lite_123`

### Amount Conversion
- Input TON amount is multiplied by 1,000,000,000 to convert to nanoTON
- Blockchain validation requires exact nanoTON amount match

### Boost Calculation
- Speed boost is calculated using BoostService based on TON amount
- Boost duration is 30 days from application
- Previous mining progress is saved before applying new speed

### Transaction Recording
- Creates transaction record with type `boost_payment`
- Status is set to `completed` upon successful validation
- Includes user ID, amount, and descriptive message

## Error Handling

The endpoint handles the following error scenarios:

1. **Invalid Input**: Returns 422 with validation errors
2. **Blockchain Validation Failure**: Returns 400 with expected transaction details
3. **Boost Application Failure**: Returns 500 with error message
4. **Database Errors**: Wrapped in database transaction with rollback

## Security Considerations

- User authentication required via middleware
- Blockchain validation prevents payment fraud
- Database transactions ensure data consistency
- All errors are logged for monitoring

## Integration Example

```javascript
// Example client-side integration
const response = await fetch('/api/user/boost', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + userToken
  },
  body: JSON.stringify({
    ton_amount: 5.0
  })
});

const result = await response.json();
if (result.success) {
  console.log('Boost applied:', result.data.new_speed);
} else {
  console.error('Boost failed:', result.message);
}
```

## Related Services

- **BoostService**: Handles speed calculation and application
- **TonHelper**: Provides blockchain validation utilities
- **Transaction Model**: Records payment transactions