# LeaderBoard API Documentation

## Overview
The LeaderBoard API provides endpoints for displaying top-performing users based on mining speed and TON balance. It features a competitive ranking system with realistic fake top users to make the leaderboard more engaging.

## Base URL
```
/api/user/leaderboard/
```

## Authentication
All endpoints require user authentication (UserAuth middleware).

## Endpoints

### 1. Top Speed Leaderboard
**GET** `/user/leaderboard/speed`

Retrieves the top 20 users ranked by mining speed.

#### Request Parameters
None

#### Response
**Success (200)**
```json
{
    "success": true,
    "data": {
        "type": "speed",
        "leaderboard": [
            {
                "rank": 1,
                "name": "CryptoMiner2024",
                "username": "@cryptominer2024",
                "speed": 18.75,
                "avatar_url": "https://api.dicebear.com/7.x/avataaars/svg?seed=CryptoMiner2024",
                "is_current_user": false
            },
            {
                "rank": 2,
                "name": "HashMaster",
                "username": "@hashmaster",
                "speed": 17.42,
                "avatar_url": "https://api.dicebear.com/7.x/avataaars/svg?seed=HashMaster",
                "is_current_user": false
            },
            {
                "rank": 9,
                "name": "Real User",
                "username": "@realuser",
                "speed": 5.25,
                "avatar_url": "https://telegram.org/img/t_logo.png",
                "is_current_user": true
            }
        ],
        "total_users": 20,
        "updated_at": "2025-07-14T12:30:00.000000Z"
    }
}
```

#### Features
- **Top 8 positions**: Occupied by realistic fake users with competitive speeds (11.76 - 18.75 TON/h)
- **Positions 9-20**: Filled with real users from the database
- **Current User Highlighting**: The authenticated user is marked with `is_current_user: true`
- **Realistic Usernames**: Fake users have crypto-themed usernames
- **Avatar Generation**: Uses DiceBear API for consistent fake user avatars

---

### 2. Top Balance Leaderboard
**GET** `/user/leaderboard/balance`

Retrieves the top 20 users ranked by TON balance.

#### Request Parameters
None

#### Response
**Success (200)**
```json
{
    "success": true,
    "data": {
        "type": "balance",
        "leaderboard": [
            {
                "rank": 1,
                "name": "TON_Whale",
                "username": "@ton_whale",
                "balance": 2847.52,
                "avatar_url": "https://api.dicebear.com/7.x/avataaars/svg?seed=TON_Whale",
                "is_current_user": false
            },
            {
                "rank": 2,
                "name": "DiamondHands",
                "username": "@diamondhands",
                "balance": 2156.89,
                "avatar_url": "https://api.dicebear.com/7.x/avataaars/svg?seed=DiamondHands",
                "is_current_user": false
            },
            {
                "rank": 9,
                "name": "Real User",
                "username": "@realuser",
                "balance": 125.50,
                "avatar_url": "https://telegram.org/img/t_logo.png",
                "is_current_user": true
            }
        ],
        "total_users": 20,
        "updated_at": "2025-07-14T12:30:00.000000Z"
    }
}
```

#### Features
- **Top 8 positions**: Occupied by realistic fake users with high balances (956.82 - 2847.52 TON)
- **Positions 9-20**: Filled with real users from the database
- **Current User Highlighting**: The authenticated user is marked with `is_current_user: true`
- **Whale-themed Names**: Fake users have names reflecting high-value cryptocurrency terms
- **Avatar Generation**: Uses DiceBear API for consistent fake user avatars

---

### 3. User Rank Information
**GET** `/user/leaderboard/my-rank`

Retrieves the current user's ranking information for both speed and balance leaderboards.

#### Request Parameters
None

#### Response
**Success (200)**
```json
{
    "success": true,
    "data": {
        "user_id": 123,
        "name": "John Doe",
        "username": "johndoe",
        "speed": 3.25,
        "balance_ton": 45.50,
        "ranks": {
            "speed": 15,
            "balance": 12
        }
    }
}
```

**Error Responses**
- **401 Unauthorized**: User not authenticated

#### Features
- **Real-time Ranking**: Calculates user's position among all users plus fake users
- **Dual Rankings**: Shows position in both speed and balance leaderboards
- **User Stats**: Includes current speed and balance values
- **Rank Calculation**: Adds 8 positions to account for fake top users

---

## Fake User Data

### Speed Leaderboard Fake Users
| Rank | Name | Username | Speed (TON/h) | Theme |
|------|------|----------|---------------|--------|
| 1 | CryptoMiner2024 | @cryptominer2024 | 18.75 | General Mining |
| 2 | HashMaster | @hashmaster | 17.42 | Hash Power |
| 3 | TON_Wizard | @ton_wizard | 16.89 | TON Expert |
| 4 | DigitalGold | @digitalgold | 15.67 | Digital Assets |
| 5 | BlockchainKing | @blockchainKing | 14.93 | Blockchain |
| 6 | MiningLegend | @mininglegend | 13.28 | Mining Hero |
| 7 | TON_Hunter | @ton_hunter | 12.54 | TON Collector |
| 8 | CryptoNinja | @cryptoninja | 11.76 | Stealth Mining |

### Balance Leaderboard Fake Users
| Rank | Name | Username | Balance (TON) | Theme |
|------|------|----------|---------------|--------|
| 1 | TON_Whale | @ton_whale | 2847.52 | Whale Investor |
| 2 | DiamondHands | @diamondhands | 2156.89 | HODLer |
| 3 | CryptoTycoon | @cryptotycoon | 1876.34 | Business Mogul |
| 4 | BlockchainBaron | @blockchainbaron | 1654.27 | Aristocrat |
| 5 | TON_Collector | @ton_collector | 1432.98 | Collector |
| 6 | CryptoSavant | @cryptosavant | 1298.76 | Expert |
| 7 | DigitalMogul | @digitalmogul | 1087.43 | Digital Empire |
| 8 | TON_Master | @ton_master | 956.82 | Master Level |

## Data Models

### Leaderboard Entry Object
```json
{
    "rank": 1,
    "name": "User Name",
    "username": "@username",
    "speed": 15.75,           // For speed leaderboard
    "balance": 1234.56,       // For balance leaderboard
    "avatar_url": "https://api.dicebear.com/7.x/avataaars/svg?seed=UserName",
    "is_current_user": false
}
```

### User Rank Object
```json
{
    "user_id": 123,
    "name": "User Name",
    "username": "username",
    "speed": 3.25,
    "balance_ton": 45.50,
    "ranks": {
        "speed": 15,
        "balance": 12
    }
}
```

## Implementation Details

### Ranking Algorithm
1. **Fake Users**: 8 predefined fake users occupy positions 1-8
2. **Real Users**: Retrieved from database, ordered by respective metric
3. **Positioning**: Real users start from position 9
4. **Limit**: Total leaderboard capped at 20 users
5. **Current User**: Highlighted when present in top 20

### Avatar Generation
- **Fake Users**: DiceBear Avataaars API with consistent seeds
- **Real Users**: Use Telegram avatar_url if available
- **Fallback**: Default avatar for users without profile pictures

### Performance Considerations
- **Database Queries**: Optimized with proper indexing on speed and balance_ton columns
- **Caching**: Consider implementing Redis caching for frequently accessed leaderboards
- **Pagination**: Limited to top 20 to maintain performance

## Usage Examples

### Get speed leaderboard
```bash
curl -X GET "http://localhost:8000/api/user/leaderboard/speed" \
  -H "Authorization: Bearer YOUR_USER_TOKEN"
```

### Get balance leaderboard
```bash
curl -X GET "http://localhost:8000/api/user/leaderboard/balance" \
  -H "Authorization: Bearer YOUR_USER_TOKEN"
```

### Get current user rank
```bash
curl -X GET "http://localhost:8000/api/user/leaderboard/my-rank" \
  -H "Authorization: Bearer YOUR_USER_TOKEN"
```

## Error Handling

All endpoints return consistent error responses:

```json
{
    "success": false,
    "message": "Error description"
}
```

### Common HTTP Status Codes
- `200` - Success
- `401` - Unauthorized (invalid or missing token)
- `500` - Internal Server Error

## Security Considerations

### Authentication
- All endpoints require valid user authentication
- User tokens validated via UserAuth middleware
- Protected against unauthorized access

### Data Privacy
- Users only see public profile information
- No sensitive data exposed in leaderboards
- Current user identification only for authenticated user

### Rate Limiting
- Consider implementing rate limiting for frequent requests
- Prevent abuse of leaderboard endpoints
- Monitor for excessive API calls

## Future Enhancements

### Potential Features
1. **Time-based Leaderboards**: Daily, weekly, monthly rankings
2. **Category Filters**: Filter by country, user level, etc.
3. **Historical Data**: Track ranking changes over time
4. **Rewards System**: Integrate with achievement/reward system
5. **Real-time Updates**: WebSocket support for live rankings
6. **User Profiles**: Detailed user statistics and achievements

### Technical Improvements
1. **Caching Strategy**: Implement Redis for better performance
2. **Database Indexing**: Optimize queries with proper indexes
3. **Pagination**: Support for viewing beyond top 20
4. **API Versioning**: Version control for future changes
5. **Monitoring**: Add metrics for API performance tracking