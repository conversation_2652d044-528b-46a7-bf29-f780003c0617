# Transaction API Documentation

## Overview
The Transaction API provides endpoints for retrieving transaction data with filtering and pagination capabilities. There are two main endpoints: one for admin users to view all transactions and another for regular users to view their own transactions.

## Endpoints

### GET /api/user/transactions

**Description:** Retrieve transactions for the authenticated user

**Authentication:** Required (user.auth middleware)

**Content-Type:** application/json

## Request Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `type` | string | No | Filter transactions by group type. Valid values: `deposit`, `withdrawal`, `bonus` |
| `per_page` | integer | No | Number of transactions per page (1-50, default: 15) |
| `page` | integer | No | Page number (minimum: 1) |

## Request Examples

### Get all user transactions
```bash
GET /api/user/transactions
```

### Get user transactions with filtering
```bash
GET /api/user/transactions?type=deposit&per_page=10&page=1
```

## Response Format

### Success Response (200 OK)
```json
{
  "success": true,
  "message": "Your transactions retrieved successfully",
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "type": "boost_payment",
        "value": "5.00000000",
        "description": "Mining speed boost purchase",
        "status": "completed",
        "hash": "tx_abc123def456",
        "created_at": "2024-01-15T10:30:00.000000Z",
        "updated_at": "2024-01-15T10:30:00.000000Z",
        "group": "deposit"
      },
      {
        "id": 2,
        "type": "referral_bonus",
        "value": "1.50000000",
        "description": "Referral commission bonus",
        "status": "completed",
        "hash": "tx_def456ghi789",
        "created_at": "2024-01-14T09:15:00.000000Z",
        "updated_at": "2024-01-14T09:15:00.000000Z",
        "group": "bonus"
      }
    ],
    "first_page_url": "http://localhost/api/user/transactions?page=1",
    "from": 1,
    "last_page": 5,
    "last_page_url": "http://localhost/api/user/transactions?page=5",
    "links": [...],
    "next_page_url": "http://localhost/api/user/transactions?page=2",
    "path": "http://localhost/api/user/transactions",
    "per_page": 15,
    "prev_page_url": null,
    "to": 15,
    "total": 67
  }
}
```

### Error Response (422 Unprocessable Entity)
```json
{
  "message": "The given data was invalid.",
  "errors": {
    "group": ["The selected group is invalid."],
    "per_page": ["The per page must be between 1 and 50."]
  }
}
```

---

### GET /api/admin/transactions

**Description:** Retrieve transactions for admin users (all transactions or filtered by user)

**Authentication:** Required (admin.auth middleware)

**Content-Type:** application/json

## Request Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `user_id` | integer | No | Filter transactions by specific user ID |
| `group` | string | No | Filter transactions by group type. Valid values: `deposit`, `withdrawal`, `bonus` |
| `per_page` | integer | No | Number of transactions per page (1-100, default: 15) |
| `page` | integer | No | Page number (minimum: 1) |

## Request Examples

### Get all transactions
```bash
GET /api/admin/transactions
```

### Get transactions for specific user
```bash
GET /api/admin/transactions?user_id=123
```

### Get transactions with filtering
```bash
GET /api/admin/transactions?group=withdrawal&per_page=25&page=1
```

### Get transactions for specific user and group
```bash
GET /api/admin/transactions?user_id=123&group=deposit
```

## Response Format

### Success Response (200 OK)
```json
{
  "success": true,
  "message": "Transactions retrieved successfully",
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "type": "boost_payment",
        "value": "5.00000000",
        "description": "Mining speed boost purchase",
        "status": "completed",
        "hash": "tx_abc123def456",
        "user_id": 123,
        "created_at": "2024-01-15T10:30:00.000000Z",
        "updated_at": "2024-01-15T10:30:00.000000Z",
        "group": "deposit",
        "user": {
          "id": 123,
          "first_name": "John",
          "last_name": "Doe",
          "username": "johndoe",
          "tele_id": "987654321"
        }
      },
      {
        "id": 2,
        "type": "withdrawal",
        "value": "10.00000000",
        "description": "User withdrawal request",
        "status": "pending",
        "hash": "tx_def456ghi789",
        "user_id": 456,
        "created_at": "2024-01-14T09:15:00.000000Z",
        "updated_at": "2024-01-14T09:15:00.000000Z",
        "group": "withdrawal",
        "user": {
          "id": 456,
          "first_name": "Jane",
          "last_name": "Smith",
          "username": "janesmith",
          "tele_id": "123456789"
        }
      }
    ],
    "first_page_url": "http://localhost/api/admin/transactions?page=1",
    "from": 1,
    "last_page": 8,
    "last_page_url": "http://localhost/api/admin/transactions?page=8",
    "links": [...],
    "next_page_url": "http://localhost/api/admin/transactions?page=2",
    "path": "http://localhost/api/admin/transactions",
    "per_page": 15,
    "prev_page_url": null,
    "to": 15,
    "total": 120
  }
}
```

### Error Response (422 Unprocessable Entity)
```json
{
  "message": "The given data was invalid.",
  "errors": {
    "user_id": ["The selected user id is invalid."],
    "group": ["The selected group is invalid."],
    "per_page": ["The per page must be between 1 and 100."]
  }
}
```

## Transaction Groups

The API supports filtering by transaction groups, which categorize different types of transactions:

### Deposit Group
- `boost_payment`: Mining speed boost purchases

### Withdrawal Group
- `withdrawal`: User withdrawal requests

### Bonus Group
- `referral_bonus`: Referral commission bonuses

## Transaction Fields

| Field | Type | Description |
|-------|------|-------------|
| `id` | integer | Unique transaction identifier |
| `type` | string | Transaction type (enum value) |
| `value` | string | Transaction amount (decimal with 8 precision) |
| `description` | string | Human-readable transaction description |
| `status` | string | Transaction status (pending, completed, failed, etc.) |
| `hash` | string | Unique transaction hash (format: tx_[32 chars]) |
| `user_id` | integer | ID of the user who owns the transaction |
| `created_at` | string | ISO 8601 timestamp of creation |
| `updated_at` | string | ISO 8601 timestamp of last update |
| `group` | string | Transaction group (computed field) |
| `user` | object | User information (admin endpoint only) |

## Authentication

### User Endpoint
- Requires user authentication via `user.auth` middleware
- Users can only view their own transactions
- Route: `/api/user/transactions`

### Admin Endpoint
- Requires admin authentication via `admin.auth` middleware
- Admins can view all transactions and filter by user
- Route: `/api/admin/transactions`

## Rate Limiting

Both endpoints are subject to standard Laravel rate limiting. Ensure proper handling of rate limit responses.

## Error Handling

All endpoints return standard HTTP status codes:
- `200 OK`: Successful request
- `401 Unauthorized`: Authentication required or invalid
- `422 Unprocessable Entity`: Validation errors
- `500 Internal Server Error`: Server error

For validation errors, the response includes detailed error messages for each invalid field.
