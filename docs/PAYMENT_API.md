# TON Payment API Documentation

## Overview

This document describes the TON payment system for boost payments. The system handles a complete payment lifecycle from initialization to completion with blockchain verification.

## Payment Flow

1. **Client**: Sends TON amount needed for boost to backend
2. **Backend**: Initializes transaction with pending status
3. **Client**: Sends TON amount to the provided address with the generated message
4. **Client**: Sends completion request to backend
5. **Backend**: Validates actual received TON amount on blockchain
6. **Backend**: Marks transaction as completed if validation successful

## API Endpoints

### Initialize Boost Payment

**Endpoint:** `POST /api/user/ton-payment/message`

**Authentication:** Bearer token required

**Request Body:**
```json
{
    "ton": 0.5
}
```

**Response (Success):**
```json
{
    "success": true,
    "message": "Message built successfully",
    "data": {
        "transaction_hash": "abc123def456",
        "message": "starapp_abc123def456",
        "address": "0QBDsPeyD5NOPNekQfdFXgNVGIcgXDYH0j7N-fbSIdYzi5Bg",
        "ton_amount": 0.5
    }
}
```

**Response (Error):**
```json
{
    "success": false,
    "message": "Invalid TON amount",
    "errors": {
        "ton": ["The ton field is required."]
    }
}
```

### Complete Boost Payment

**Endpoint:** `POST /api/user/ton-payment/complete`

**Authentication:** Bearer token required

**Request Body:**
```json
{
    "hash": "abc123def456"
}
```

**Response (Success):**
```json
{
    "success": true,
    "message": "Transaction completed successfully and order created.",
    "data": {
        "transaction_hash": "abc123def456",
        "status": "completed"
    }
}
```

**Response (Transaction Not Found):**
```json
{
    "success": false,
    "message": "No pending transaction found"
}
```

**Response (Blockchain Validation Failed):**
```json
{
    "success": false,
    "message": "Transaction not found on blockchain or failed",
    "data": {
        "transaction_hash": "abc123def456",
        "status": "pending"
    }
}
```

## Key Features

### 1. Duplicate Prevention
- **5-minute window**: Checks for existing pending transactions within last 5 minutes
- **Reuse logic**: Returns existing transaction if found with same TON amount
- **Cleanup**: Removes other pending transactions for the same user when reusing

### 2. Transaction Management
- **Unique hash generation**: Each transaction gets a 16-character random hash
- **Status tracking**: PENDING → COMPLETED workflow
- **Value storage**: TON amounts stored in nanoTON (multiply by 1,000,000,000)

### 3. Blockchain Verification
- **TonHelper integration**: Uses TON blockchain API to verify payments
- **Address validation**: Checks payments sent to configured receiver address
- **Message validation**: Validates transaction message format matches expected pattern

### 4. Error Handling
- **Missing transactions**: Returns 404 if no pending transaction found
- **Blockchain failures**: Returns 400 if blockchain verification fails
- **Database errors**: Uses database transactions with rollback on failure

## Transaction Lifecycle

```
1. Client Request (ton: 0.5)
   ↓
2. Backend Check (existing pending transaction within 5 minutes?)
   ↓
3a. If exists: Return existing transaction
3b. If not exists: Create new transaction
   ↓
4. Return transaction hash and payment details
   ↓
5. Client sends TON to address with message
   ↓
6. Client calls complete endpoint with hash
   ↓
7. Backend validates on blockchain
   ↓
8a. If valid: Mark as completed
8b. If invalid: Return error
```

## Request Validation

### Initialize Payment
- `ton`: Required, numeric, converted to nanoTON for storage

### Complete Payment
- `hash`: Required, must match existing pending transaction hash
- Must belong to authenticated user
- Transaction must be in PENDING status

## Security Considerations

1. **Authentication**: All endpoints require valid bearer token
2. **User isolation**: Users can only access their own transactions
3. **Transaction uniqueness**: Hash prevents replay attacks
4. **Time-based cleanup**: 5-minute window prevents abuse
5. **Blockchain verification**: Ensures actual payment before completion

## Error Codes

- **200**: Success
- **400**: Blockchain validation failed
- **404**: Transaction not found
- **422**: Validation error
- **500**: Server error

## Database Schema

**Transactions Table:**
- `user_id`: Foreign key to users table
- `type`: BOOST_PAYMENT enum value
- `hash`: 16-character random string
- `status`: PENDING/COMPLETED enum value
- `description`: Human-readable description
- `value`: TON amount in nanoTON
- `created_at`: Timestamp for 5-minute window logic

## Environment Variables

- `TON_RECEIVER_ADDRESS`: Address where payments are received
- TON API configuration for blockchain verification

### cURL Examples

```bash
# Initialize payment
curl -X POST "https://api.example.com/api/user/ton-payment/message" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"ton": 0.5}'

# Complete payment
curl -X POST "https://api.example.com/api/user/ton-payment/complete" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"hash": "abc123def456"}'
```
