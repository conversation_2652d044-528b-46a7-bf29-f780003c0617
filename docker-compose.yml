version: '3.8'

services:
  # Backend API Service
  backend:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        USER_ID: ${USER_ID:-1000}
        GROUP_ID: ${GROUP_ID:-1000}
    container_name: backend-api
    restart: unless-stopped
    network_mode: host
    environment:
      - APP_ENV=local
      - APP_DEBUG=true
      - DB_CONNECTION=mariadb
      - DB_HOST=127.0.0.1
      - DB_PORT=3307
      - DB_DATABASE=backend
      - DB_USERNAME=root
      - DB_PASSWORD=secret
      - REDIS_HOST=127.0.0.1
      - REDIS_PORT=6380
      - CACHE_STORE=redis
      - SESSION_DRIVER=redis
      - QUEUE_CONNECTION=redis
    volumes:
      - .:/app
    depends_on:
      - mariadb
      - redis

  # MariaDB Database Service
  mariadb:
    image: mariadb:10.11
    container_name: backend-mariadb
    restart: unless-stopped
    ports:
      - "3307:3306"
    environment:
      MYSQL_ROOT_PASSWORD: secret
      MYSQL_DATABASE: backend
      MYSQL_USER: backend_user
      MYSQL_PASSWORD: backend_password
    volumes:
      - mariadb_data:/var/lib/mysql
      - ./database/mysql-init:/docker-entrypoint-initdb.d
    networks:
      - backend-network

  # Redis Cache Service
  redis:
    image: redis:7.2-alpine
    container_name: backend-redis
    restart: unless-stopped
    ports:
      - "6380:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - backend-network

  # phpMyAdmin Service
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: backend-phpmyadmin
    restart: unless-stopped
    ports:
      - "8080:80"
    environment:
      PMA_HOST: mariadb
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: secret
      MYSQL_ROOT_PASSWORD: secret
    depends_on:
      - mariadb
    networks:
      - backend-network

volumes:
  mariadb_data:
    driver: local
  redis_data:
    driver: local

networks:
  backend-network:
    driver: bridge
